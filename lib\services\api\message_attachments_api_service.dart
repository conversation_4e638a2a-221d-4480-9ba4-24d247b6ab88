import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../../models/message_attachment_models.dart';
import 'api_service.dart';

/// خدمة API لمرفقات الرسائل - متوافقة مع ASP.NET Core
class MessageAttachmentsApiService extends GetxService {
  late final ApiService _apiService;

  MessageAttachmentsApiService() {
    // محاولة الحصول على ApiService من GetX، أو إنشاء واحد جديد
    try {
      _apiService = Get.find<ApiService>();
    } catch (e) {
      _apiService = ApiService();
    }
  }

  /// الحصول على جميع المرفقات
  Future<List<MessageAttachment>> getAllAttachments() async {
    try {
      final response = await _apiService.get('/api/MessageAttachments');
      return _apiService.handleListResponse<MessageAttachment>(
        response,
        (json) => MessageAttachment.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل المرفقات: $e');
    }
  }

  /// الحصول على مرفقات رسالة محددة
  Future<List<MessageAttachment>> getAttachmentsByMessage(int messageId) async {
    try {
      final response = await _apiService.get('/api/MessageAttachments/message/$messageId');
      return _apiService.handleListResponse<MessageAttachment>(
        response,
        (json) => MessageAttachment.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل مرفقات الرسالة: $e');
    }
  }

  /// الحصول على مرفق بالمعرف
  Future<MessageAttachment> getAttachmentById(int id) async {
    try {
      final response = await _apiService.get('/api/MessageAttachments/$id');
      return _apiService.handleResponse<MessageAttachment>(
        response,
        (json) => MessageAttachment.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل المرفق: $e');
    }
  }

  /// رفع مرفق جديد (الطريقة القديمة - مهجورة)
  @Deprecated('استخدم uploadAttachmentFile بدلاً من ذلك')
  Future<MessageAttachment> uploadAttachment(int messageId, String filePath, String fileName) async {
    // تحويل إلى File object واستخدام الطريقة الجديدة
    final file = File(filePath);
    return await uploadAttachmentFile(
      file: file,
      messageId: messageId,
      uploadedBy: 1, // معرف افتراضي - يجب تمريره من المتصل
    );
  }

  /// رفع مرفق جديد للرسالة (الطريقة الصحيحة)
  Future<MessageAttachment> uploadAttachmentFile({
    required File file,
    required int messageId,
    required int uploadedBy,
    String? description,
    Function(double)? onUploadProgress,
  }) async {
    try {
      debugPrint('🔄 بدء رفع مرفق رسالة: ${file.path}');
      debugPrint('📋 تفاصيل الرفع:');
      debugPrint('   📄 مسار الملف: ${file.path}');
      debugPrint('   📏 حجم الملف: ${await file.length()} bytes');
      debugPrint('   💬 معرف الرسالة: $messageId');
      debugPrint('   👤 معرف المستخدم: $uploadedBy');
      debugPrint('   📝 الوصف: ${description ?? "غير محدد"}');

      // التحقق من وجود الملف
      if (!await file.exists()) {
        throw Exception('الملف غير موجود: ${file.path}');
      }

      // استخدام postMultipart مثل نظام المهام
      final response = await _apiService.postMultipart(
        '/api/MessageAttachments/upload',
        file: file,
        fields: {
          'messageId': messageId.toString(),
          'uploadedBy': uploadedBy.toString(),
          if (description != null) 'description': description,
        },
        onUploadProgress: onUploadProgress,
      );

      debugPrint('📡 استجابة الخادم:');
      debugPrint('   🔢 كود الحالة: ${response.statusCode}');
      debugPrint('   📄 محتوى الاستجابة: ${response.body}');

      if (response.statusCode >= 200 && response.statusCode < 300) {
        debugPrint('✅ تم رفع مرفق الرسالة بنجاح');
      } else {
        debugPrint('❌ فشل في رفع المرفق - كود الحالة: ${response.statusCode}');
      }

      return _apiService.handleResponse<MessageAttachment>(
        response,
        (json) => MessageAttachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('❌ خطأ في رفع مرفق الرسالة: $e');
      throw Exception('خطأ في رفع المرفق: $e');
    }
  }

  /// تحديث مرفق
  Future<MessageAttachment> updateAttachment(MessageAttachment attachment) async {
    try {
      final response = await _apiService.put('/api/MessageAttachments/${attachment.id}', attachment.toJson());
      return _apiService.handleResponse<MessageAttachment>(
        response,
        (json) => MessageAttachment.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحديث المرفق: $e');
    }
  }

  /// حذف مرفق
  Future<void> deleteAttachment(int id) async {
    try {
      await _apiService.delete('/api/MessageAttachments/$id');
    } catch (e) {
      throw Exception('خطأ في حذف المرفق: $e');
    }
  }

  /// تحميل مرفق
  Future<String> downloadAttachment(int attachmentId) async {
    try {
      final response = await _apiService.get('/api/MessageAttachments/$attachmentId/download');
      return _apiService.handleResponse<String>(
        response,
        (json) => json['downloadUrl'] as String,
      );
    } catch (e) {
      throw Exception('خطأ في تحميل المرفق: $e');
    }
  }

  /// البحث في المرفقات
  Future<List<MessageAttachment>> searchAttachments(String query, Map<String, dynamic>? filters) async {
    try {
      final queryParams = {
        'q': query,
        ...?filters?.map((key, value) => MapEntry(key, value.toString())),
      };
      final response = await _apiService.get('/api/MessageAttachments/search', queryParams: queryParams);
      return _apiService.handleListResponse<MessageAttachment>(
        response,
        (json) => MessageAttachment.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في البحث في المرفقات: $e');
    }
  }

  /// الحصول على المرفقات حسب النوع
  Future<List<MessageAttachment>> getAttachmentsByType(String fileType) async {
    try {
      final response = await _apiService.get('/api/MessageAttachments/type/$fileType');
      return _apiService.handleListResponse<MessageAttachment>(
        response,
        (json) => MessageAttachment.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل المرفقات حسب النوع: $e');
    }
  }

  /// الحصول على المرفقات حسب المستخدم
  Future<List<MessageAttachment>> getAttachmentsByUser(int userId) async {
    try {
      final response = await _apiService.get('/api/MessageAttachments/user/$userId');
      return _apiService.handleListResponse<MessageAttachment>(
        response,
        (json) => MessageAttachment.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل مرفقات المستخدم: $e');
    }
  }

  /// الحصول على إحصائيات المرفقات
  Future<Map<String, dynamic>> getAttachmentsStats() async {
    try {
      final response = await _apiService.get('/api/MessageAttachments/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات المرفقات: $e');
    }
  }

  /// تنظيف المرفقات المحذوفة
  Future<void> cleanupDeletedAttachments() async {
    try {
      await _apiService.post('/api/MessageAttachments/cleanup', {});
    } catch (e) {
      throw Exception('خطأ في تنظيف المرفقات: $e');
    }
  }

  /// التحقق من صحة المرفق
  Future<bool> validateAttachment(int attachmentId) async {
    try {
      final response = await _apiService.post('/api/MessageAttachments/$attachmentId/validate', {});
      return _apiService.handleResponse<bool>(
        response,
        (json) => json['isValid'] as bool,
      );
    } catch (e) {
      throw Exception('خطأ في التحقق من صحة المرفق: $e');
    }
  }

  /// الحصول على معاينة المرفق
  Future<String> getAttachmentPreview(int attachmentId) async {
    try {
      final response = await _apiService.get('/api/MessageAttachments/$attachmentId/preview');
      return _apiService.handleResponse<String>(
        response,
        (json) => json['previewUrl'] as String,
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على معاينة المرفق: $e');
    }
  }

  /// تحديد نوع الملف من الاسم
  String _getFileType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return 'image';
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'document';
      case 'xls':
      case 'xlsx':
        return 'spreadsheet';
      case 'mp4':
      case 'avi':
      case 'mov':
        return 'video';
      case 'mp3':
      case 'wav':
      case 'ogg':
        return 'audio';
      case 'zip':
      case 'rar':
      case '7z':
        return 'archive';
      default:
        return 'other';
    }
  }
}
