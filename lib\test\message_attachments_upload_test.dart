import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import '../controllers/auth_controller.dart';
import '../services/api/message_attachments_api_service.dart';
import '../services/api/messages_api_service.dart';
import '../models/message_models.dart';

/// شاشة اختبار رفع مرفقات الرسائل
class MessageAttachmentsUploadTestScreen extends StatefulWidget {
  const MessageAttachmentsUploadTestScreen({super.key});

  @override
  State<MessageAttachmentsUploadTestScreen> createState() => _MessageAttachmentsUploadTestScreenState();
}

class _MessageAttachmentsUploadTestScreenState extends State<MessageAttachmentsUploadTestScreen> {
  final _authController = Get.find<AuthController>();
  final _attachmentsApi = MessageAttachmentsApiService();
  final _messagesApi = MessagesApiService();
  final _testResults = <String>[].obs;
  final _messageIdController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار رفع مرفقات الرسائل'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // معلومات النظام
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'حالة النظام',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Obx(() => Text(
                      'المستخدم الحالي: ${_authController.currentUser.value?.name ?? "غير مسجل"}',
                    )),
                    Obx(() => Text(
                      'معرف المستخدم: ${_authController.currentUser.value?.id ?? "غير محدد"}',
                    )),
                    const Divider(),
                    const Text(
                      'ملاحظة: تأكد من أن Backend يعمل على المنفذ 7111',
                      style: TextStyle(
                        color: Colors.orange,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Text(
                      'إذا فشل الرفع، راجع logs في Backend و Frontend',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // إدخال معرف الرسالة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معرف الرسالة للاختبار',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _messageIdController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'معرف الرسالة',
                        hintText: 'أدخل معرف رسالة موجودة',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _createTestMessage,
                            child: const Text('إنشاء رسالة اختبار'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              _messageIdController.text = '1'; // معرف افتراضي
                              _addResult('ℹ️ تم تعيين معرف رسالة افتراضي: 1');
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey,
                            ),
                            child: const Text('استخدام معرف افتراضي'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // أزرار الاختبار
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'اختبارات الرفع',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // اختبار رفع ملف
                    ElevatedButton(
                      onPressed: _testFileUpload,
                      child: const Text('اختبار رفع ملف'),
                    ),
                    const SizedBox(height: 8),
                    
                    // اختبار رفع صورة
                    ElevatedButton(
                      onPressed: _testImageUpload,
                      child: const Text('اختبار رفع صورة'),
                    ),
                    const SizedBox(height: 8),
                    
                    // اختبار API مباشرة
                    ElevatedButton(
                      onPressed: _testApiDirectly,
                      child: const Text('اختبار API مباشرة'),
                    ),
                    const SizedBox(height: 8),
                    
                    // مسح النتائج
                    ElevatedButton(
                      onPressed: _clearResults,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey,
                      ),
                      child: const Text('مسح النتائج'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // نتائج الاختبار
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'نتائج الاختبار',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: Obx(() => ListView.builder(
                          itemCount: _testResults.length,
                          itemBuilder: (context, index) {
                            final result = _testResults[index];
                            final isError = result.startsWith('❌');
                            final isSuccess = result.startsWith('✅');
                            final isWarning = result.startsWith('⚠️');
                            
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2),
                              child: Text(
                                result,
                                style: TextStyle(
                                  color: isError ? Colors.red : 
                                         isSuccess ? Colors.green :
                                         isWarning ? Colors.orange :
                                         Colors.black87,
                                  fontFamily: 'monospace',
                                  fontSize: 12,
                                ),
                              ),
                            );
                          },
                        )),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// إنشاء رسالة اختبار
  Future<void> _createTestMessage() async {
    _addResult('🔄 إنشاء رسالة اختبار...');
    
    try {
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) {
        _addResult('❌ لا يوجد مستخدم مسجل');
        return;
      }

      // إنشاء رسالة اختبار في مجموعة افتراضية (معرف 1)
      final testMessage = Message(
        id: 0, // سيتم تعيينه من الخادم
        groupId: 1,
        content: 'رسالة اختبار لرفع المرفقات - ${DateTime.now()}',
        senderId: currentUser.id,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        contentType: MessageContentType.text,
        isDeleted: false,
        isRead: false,
        isPinned: false,
        priority: MessagePriority.normal,
        isMarkedForFollowUp: false,
        isEdited: false,
        mentionedUserIds: [],
      );

      final message = await _messagesApi.sendMessage(testMessage);

      _messageIdController.text = message.id.toString();
      _addResult('✅ تم إنشاء رسالة اختبار - معرف: ${message.id}');
      
    } catch (e) {
      _addResult('❌ فشل في إنشاء رسالة اختبار: $e');
    }
  }

  /// اختبار رفع ملف
  Future<void> _testFileUpload() async {
    _addResult('🔄 بدء اختبار رفع ملف...');
    
    try {
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) {
        _addResult('❌ لا يوجد مستخدم مسجل');
        return;
      }

      final messageIdText = _messageIdController.text.trim();
      if (messageIdText.isEmpty) {
        _addResult('❌ يرجى إدخال معرف الرسالة');
        return;
      }

      final messageId = int.tryParse(messageIdText);
      if (messageId == null) {
        _addResult('❌ معرف الرسالة غير صحيح');
        return;
      }

      // اختيار ملف
      final result = await FilePicker.platform.pickFiles();
      if (result == null || result.files.isEmpty) {
        _addResult('❌ لم يتم اختيار ملف');
        return;
      }

      final file = File(result.files.first.path!);
      _addResult('📄 تم اختيار الملف: ${file.path}');
      _addResult('📏 حجم الملف: ${await file.length()} bytes');

      // رفع الملف
      final attachment = await _attachmentsApi.uploadAttachmentFile(
        file: file,
        messageId: messageId,
        uploadedBy: currentUser.id,
        description: 'مرفق اختبار',
      );

      _addResult('✅ تم رفع الملف بنجاح - معرف المرفق: ${attachment.id}');
      _addResult('📄 اسم الملف: ${attachment.fileName}');
      _addResult('🔗 مسار الملف: ${attachment.filePath}');
      
    } catch (e) {
      _addResult('❌ فشل في رفع الملف: $e');
    }
  }

  /// اختبار رفع صورة
  Future<void> _testImageUpload() async {
    _addResult('🔄 بدء اختبار رفع صورة...');
    
    try {
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) {
        _addResult('❌ لا يوجد مستخدم مسجل');
        return;
      }

      final messageIdText = _messageIdController.text.trim();
      if (messageIdText.isEmpty) {
        _addResult('❌ يرجى إدخال معرف الرسالة');
        return;
      }

      final messageId = int.tryParse(messageIdText);
      if (messageId == null) {
        _addResult('❌ معرف الرسالة غير صحيح');
        return;
      }

      // اختيار صورة
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
      );
      if (result == null || result.files.isEmpty) {
        _addResult('❌ لم يتم اختيار صورة');
        return;
      }

      final file = File(result.files.first.path!);
      _addResult('🖼️ تم اختيار الصورة: ${file.path}');

      // رفع الصورة
      final attachment = await _attachmentsApi.uploadAttachmentFile(
        file: file,
        messageId: messageId,
        uploadedBy: currentUser.id,
        description: 'صورة اختبار',
      );

      _addResult('✅ تم رفع الصورة بنجاح - معرف المرفق: ${attachment.id}');
      
    } catch (e) {
      _addResult('❌ فشل في رفع الصورة: $e');
    }
  }

  /// اختبار API مباشرة
  Future<void> _testApiDirectly() async {
    _addResult('🔄 اختبار API مباشرة...');
    
    try {
      // جلب جميع المرفقات
      final attachments = await _attachmentsApi.getAllAttachments();
      _addResult('✅ تم جلب ${attachments.length} مرفق من API');
      
      if (attachments.isNotEmpty) {
        final firstAttachment = attachments.first;
        _addResult('📄 أول مرفق: ${firstAttachment.fileName}');
        _addResult('📏 الحجم: ${firstAttachment.fileSize} bytes');
        _addResult('🔗 المسار: ${firstAttachment.filePath}');
      }
      
    } catch (e) {
      _addResult('❌ فشل في اختبار API: $e');
    }
  }

  /// إضافة نتيجة اختبار
  void _addResult(String result) {
    _testResults.add('${DateTime.now().toString().substring(11, 19)} - $result');
  }

  /// مسح النتائج
  void _clearResults() {
    _testResults.clear();
    _addResult('🧹 تم مسح النتائج');
  }

  @override
  void dispose() {
    _messageIdController.dispose();
    super.dispose();
  }
}
