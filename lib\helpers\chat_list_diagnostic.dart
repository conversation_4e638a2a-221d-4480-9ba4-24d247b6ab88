import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../controllers/unified_chat_controller.dart';
import '../controllers/auth_controller.dart';
import '../services/api/chat_groups_api_service.dart';
import '../services/api/messages_api_service.dart';

/// مساعد تشخيص قائمة المحادثات
class ChatListDiagnostic {
  
  /// تشخيص شامل لقائمة المحادثات
  static Future<void> runFullDiagnostic() async {
    debugPrint('🔍 === بدء التشخيص الشامل لقائمة المحادثات ===');
    
    // 1. فحص المتحكمات
    await _checkControllers();
    
    // 2. فحص البيانات المحملة
    await _checkLoadedData();
    
    // 3. فحص عدد الأعضاء
    await _checkMemberCounts();
    
    // 4. فحص الرسائل غير المقروءة
    await _checkUnreadCounts();
    
    // 5. فحص API
    await _checkApiResponses();
    
    debugPrint('✅ === انتهى التشخيص الشامل ===');
  }

  /// فحص المتحكمات
  static Future<void> _checkControllers() async {
    debugPrint('🔍 فحص المتحكمات...');
    
    try {
      // فحص UnifiedChatController
      final chatController = Get.find<UnifiedChatController>();
      debugPrint('✅ UnifiedChatController موجود');
      debugPrint('📊 عدد المجموعات المحملة: ${chatController.chatGroups.length}');
      debugPrint('🔄 حالة التحميل: ${chatController.isLoading}');
      debugPrint('❌ الأخطاء: ${chatController.error.isEmpty ? "لا توجد" : chatController.error}');
      
      // فحص AuthController
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;
      debugPrint('✅ AuthController موجود');
      debugPrint('👤 المستخدم الحالي: ${currentUser?.name ?? "غير مسجل"}');
      debugPrint('🆔 معرف المستخدم: ${currentUser?.id ?? "غير محدد"}');
      
    } catch (e) {
      debugPrint('❌ خطأ في فحص المتحكمات: $e');
    }
  }

  /// فحص البيانات المحملة
  static Future<void> _checkLoadedData() async {
    debugPrint('🔍 فحص البيانات المحملة...');
    
    try {
      final chatController = Get.find<UnifiedChatController>();
      final groups = chatController.chatGroups;
      
      debugPrint('📊 إجمالي المجموعات: ${groups.length}');
      
      if (groups.isNotEmpty) {
        debugPrint('📋 تفاصيل أول 3 مجموعات:');
        for (int i = 0; i < groups.length && i < 3; i++) {
          final group = groups[i];
          debugPrint('  ${i + 1}. ${group.name}');
          debugPrint('     🆔 المعرف: ${group.id}');
          debugPrint('     📝 الوصف: ${group.description ?? "غير محدد"}');
          debugPrint('     🔒 خاصة: ${group.isPrivate}');
          debugPrint('     💬 محادثة مباشرة: ${group.isDirectMessage}');
          debugPrint('     👥 عدد الأعضاء: ${group.memberCount}');
          debugPrint('     📬 رسائل غير مقروءة: ${group.unreadCount}');
          debugPrint('     🔢 Backend MemberCount: ${group.backendMemberCount}');
          debugPrint('     🔢 Backend UnreadCount: ${group.backendUnreadCount}');
        }
      } else {
        debugPrint('⚠️ لا توجد مجموعات محملة');
      }
      
    } catch (e) {
      debugPrint('❌ خطأ في فحص البيانات المحملة: $e');
    }
  }

  /// فحص عدد الأعضاء
  static Future<void> _checkMemberCounts() async {
    debugPrint('🔍 فحص عدد الأعضاء...');
    
    try {
      final chatController = Get.find<UnifiedChatController>();
      final groups = chatController.chatGroups;
      
      int groupsWithZeroMembers = 0;
      int groupsWithBackendCount = 0;
      int groupsWithLocalCount = 0;
      
      for (final group in groups) {
        if (group.memberCount == 0) {
          groupsWithZeroMembers++;
          debugPrint('⚠️ مجموعة بدون أعضاء: ${group.name}');
        }
        
        if (group.backendMemberCount != null) {
          groupsWithBackendCount++;
        } else {
          groupsWithLocalCount++;
        }
      }
      
      debugPrint('📊 إحصائيات عدد الأعضاء:');
      debugPrint('   🔢 مجموعات بعدد أعضاء صفر: $groupsWithZeroMembers');
      debugPrint('   🌐 مجموعات بعدد من Backend: $groupsWithBackendCount');
      debugPrint('   📱 مجموعات بعدد محلي: $groupsWithLocalCount');
      
      if (groupsWithZeroMembers > 0) {
        debugPrint('⚠️ تحذير: يوجد مجموعات بعدد أعضاء صفر');
      }
      
    } catch (e) {
      debugPrint('❌ خطأ في فحص عدد الأعضاء: $e');
    }
  }

  /// فحص الرسائل غير المقروءة
  static Future<void> _checkUnreadCounts() async {
    debugPrint('🔍 فحص الرسائل غير المقروءة...');
    
    try {
      final chatController = Get.find<UnifiedChatController>();
      final groups = chatController.chatGroups;
      
      int groupsWithUnread = 0;
      int totalUnread = 0;
      int groupsWithBackendUnread = 0;
      
      for (final group in groups) {
        if (group.unreadCount > 0) {
          groupsWithUnread++;
          totalUnread += group.unreadCount;
          debugPrint('📬 ${group.name}: ${group.unreadCount} رسائل غير مقروءة');
        }
        
        if (group.backendUnreadCount != null) {
          groupsWithBackendUnread++;
        }
      }
      
      debugPrint('📊 إحصائيات الرسائل غير المقروءة:');
      debugPrint('   📬 مجموعات بها رسائل غير مقروءة: $groupsWithUnread');
      debugPrint('   🔢 إجمالي الرسائل غير المقروءة: $totalUnread');
      debugPrint('   🌐 مجموعات بعدد من Backend: $groupsWithBackendUnread');
      
      if (groupsWithUnread == 0) {
        debugPrint('ℹ️ لا توجد رسائل غير مقروءة');
      }
      
    } catch (e) {
      debugPrint('❌ خطأ في فحص الرسائل غير المقروءة: $e');
    }
  }

  /// فحص استجابات API
  static Future<void> _checkApiResponses() async {
    debugPrint('🔍 فحص استجابات API...');
    
    try {
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;
      
      if (currentUser == null) {
        debugPrint('❌ لا يوجد مستخدم مسجل - تخطي فحص API');
        return;
      }
      
      // فحص API المجموعات
      final chatGroupsApi = ChatGroupsApiService();
      debugPrint('🔄 اختبار API المجموعات...');
      
      final groups = await chatGroupsApi.getAllGroups();
      debugPrint('✅ تم جلب ${groups.length} مجموعة من API');
      
      if (groups.isNotEmpty) {
        final firstGroup = groups.first;
        debugPrint('📋 عينة من استجابة API:');
        debugPrint('   🆔 المعرف: ${firstGroup.id}');
        debugPrint('   📝 الاسم: ${firstGroup.name}');
        debugPrint('   👥 عدد الأعضاء: ${firstGroup.memberCount}');
        debugPrint('   📬 رسائل غير مقروءة: ${firstGroup.unreadCount}');
        debugPrint('   🔢 Backend MemberCount: ${firstGroup.backendMemberCount}');
        debugPrint('   🔢 Backend UnreadCount: ${firstGroup.backendUnreadCount}');
      }
      
      // فحص API الرسائل
      debugPrint('🔄 اختبار API الرسائل...');
      final messagesApi = MessagesApiService();
      
      // اختبار عدد الرسائل غير المقروءة
      try {
        final unreadResponse = await messagesApi.getUnreadMessageCount(currentUser.id);
        debugPrint('✅ عدد الرسائل غير المقروءة الإجمالي: $unreadResponse');
      } catch (e) {
        debugPrint('❌ فشل في جلب عدد الرسائل غير المقروءة: $e');
      }
      
    } catch (e) {
      debugPrint('❌ خطأ في فحص استجابات API: $e');
    }
  }

  /// اختبار سريع للنظام
  static Future<bool> quickTest() async {
    debugPrint('⚡ اختبار سريع لقائمة المحادثات...');
    
    try {
      // 1. فحص المتحكم
      final chatController = Get.find<UnifiedChatController>();
      
      // 2. فحص وجود مجموعات
      if (chatController.chatGroups.isEmpty) {
        debugPrint('⚠️ لا توجد مجموعات محملة');
        return false;
      }
      
      // 3. فحص البيانات الأساسية
      bool hasValidData = true;
      for (final group in chatController.chatGroups.take(3)) {
        if (group.name.isEmpty) {
          debugPrint('❌ مجموعة بدون اسم: ${group.id}');
          hasValidData = false;
        }
      }
      
      if (hasValidData) {
        debugPrint('✅ الاختبار السريع نجح - النظام يعمل');
        return true;
      } else {
        debugPrint('❌ الاختبار السريع فشل - بيانات غير صحيحة');
        return false;
      }
      
    } catch (e) {
      debugPrint('❌ الاختبار السريع فشل: $e');
      return false;
    }
  }

  /// عرض معلومات النظام
  static void showSystemInfo() {
    debugPrint('📋 === معلومات نظام قائمة المحادثات ===');
    debugPrint('🏗️ الإصدار: 2.0.0');
    debugPrint('📅 تاريخ الإصلاح: 2025-01-14');
    debugPrint('🔧 الحالة: تم إصلاح عدد الأعضاء والرسائل غير المقروءة');
    debugPrint('📝 الميزات الجديدة:');
    debugPrint('  - عرض عدد الأعضاء الصحيح من Backend');
    debugPrint('  - عرض عدد الرسائل غير المقروءة');
    debugPrint('  - تشخيص شامل للمشاكل');
    debugPrint('  - واجهة محسنة مع مؤشرات بصرية');
    debugPrint('📋 === نهاية المعلومات ===');
  }
}
