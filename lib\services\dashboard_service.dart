import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/models/dashboard_models.dart' as dashboard_models;
import 'package:get/get.dart';

/// خدمة لوحة المعلومات
class DashboardService extends GetxService {
  // لوحة المعلومات الحالية
  final Rx<dashboard_models.Dashboard?> _currentDashboard = Rx<dashboard_models.Dashboard?>(null);

  // قائمة لوحات المعلومات
  final RxList<dashboard_models.Dashboard> _dashboards = <dashboard_models.Dashboard>[].obs;

  // حالة التحميل
  final RxBool _isLoading = false.obs;

  // رسالة الخطأ
  final RxString _error = ''.obs;

  // اللوحات المتصلة
  final RxList<int> _connectedBoardIds = <int>[].obs;

  // Getters
  dashboard_models.Dashboard? get currentDashboard => _currentDashboard.value;
  List<dashboard_models.Dashboard> get dashboards => _dashboards;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  RxList<int> get connectedBoardIds => _connectedBoardIds;

  // Observable getters
  Rx<dashboard_models.Dashboard?> get currentDashboardObs => _currentDashboard;
  RxList<dashboard_models.Dashboard> get dashboardsObs => _dashboards;

  /// تهيئة الخدمة
  Future<DashboardService> init() async {
    await loadDashboards();
    return this;
  }

  /// تحميل لوحات المعلومات
  Future<void> loadDashboards() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // تحميل لوحات المعلومات من التخزين المحلي
      // TODO: تنفيذ تحميل لوحات المعلومات من StorageService
      final dashboardsData = <dashboard_models.Dashboard>[];
      _dashboards.assignAll(dashboardsData);

      // تحديد لوحة المعلومات الافتراضية
      if (_dashboards.isNotEmpty && _currentDashboard.value == null) {
        _currentDashboard.value = _dashboards.first;
      }

      debugPrint('تم تحميل ${_dashboards.length} لوحة معلومات');
    } catch (e) {
      _error.value = 'خطأ في تحميل لوحات المعلومات: $e';
      debugPrint('خطأ في تحميل لوحات المعلومات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء لوحة معلومات جديدة
  Future<dashboard_models.Dashboard> createDashboard({
    required String title,
    String? description,
    List<dashboard_models.DashboardWidget>? widgets,
  }) async {
    try {
      final dashboard = dashboard_models.Dashboard(
        id: DateTime.now().millisecondsSinceEpoch,
        title: title,
        description: description ?? '',
        ownerId: 1, // TODO: الحصول من المستخدم الحالي
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        dashboardWidgets: widgets ?? [],
      );

      _dashboards.add(dashboard);
      await _saveDashboards();

      // تعيين كلوحة افتراضية إذا كانت الأولى
      if (_dashboards.length == 1) {
        _currentDashboard.value = dashboard;
      }

      debugPrint('تم إنشاء لوحة معلومات جديدة: $title');
      return dashboard;
    } catch (e) {
      _error.value = 'خطأ في إنشاء لوحة المعلومات: $e';
      debugPrint('خطأ في إنشاء لوحة المعلومات: $e');
      rethrow;
    }
  }

  /// تحديث لوحة معلومات
  Future<void> updateDashboard(dashboard_models.Dashboard dashboard) async {
    try {
      final index = _dashboards.indexWhere((d) => d.id == dashboard.id);
      if (index != -1) {
        final updatedDashboard = dashboard.copyWith(
          updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        );
        _dashboards[index] = updatedDashboard;

        // تحديث لوحة المعلومات الحالية إذا كانت نفسها
        if (_currentDashboard.value?.id == dashboard.id) {
          _currentDashboard.value = updatedDashboard;
        }

        await _saveDashboards();
        debugPrint('تم تحديث لوحة المعلومات: ${dashboard.title}');
      }
    } catch (e) {
      _error.value = 'خطأ في تحديث لوحة المعلومات: $e';
      debugPrint('خطأ في تحديث لوحة المعلومات: $e');
    }
  }

  /// حذف لوحة معلومات
  Future<void> deleteDashboard(int dashboardId) async {
    try {
      _dashboards.removeWhere((d) => d.id == dashboardId);

      // إذا كانت لوحة المعلومات المحذوفة هي الحالية، اختر أخرى
      if (_currentDashboard.value?.id == dashboardId) {
        _currentDashboard.value = _dashboards.isNotEmpty ? _dashboards.first : null;
      }

      await _saveDashboards();
      debugPrint('تم حذف لوحة المعلومات: $dashboardId');
    } catch (e) {
      _error.value = 'خطأ في حذف لوحة المعلومات: $e';
      debugPrint('خطأ في حذف لوحة المعلومات: $e');
    }
  }

  /// تعيين لوحة المعلومات الحالية
  void setCurrentDashboard(int dashboardId) {
    final dashboard = _dashboards.firstWhereOrNull((d) => d.id == dashboardId);
    if (dashboard != null) {
      _currentDashboard.value = dashboard;
      debugPrint('تم تعيين لوحة المعلومات الحالية: ${dashboard.title}');
    }
  }

  /// إضافة عنصر إلى لوحة المعلومات
  Future<void> addWidget(String dashboardId, dashboard_models.DashboardWidget widget) async {
    try {
      final dashboardIdInt = int.tryParse(dashboardId) ?? 0;
      final dashboard = _dashboards.firstWhereOrNull((d) => d.id == dashboardIdInt);
      if (dashboard != null) {
        final updatedWidgets = List<dashboard_models.DashboardWidget>.from(dashboard.dashboardWidgets ?? [])..add(widget);
        final updatedDashboard = dashboard.copyWith(
          dashboardWidgets: updatedWidgets,
          updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        );
        await updateDashboard(updatedDashboard);
        debugPrint('تم إضافة عنصر جديد إلى لوحة المعلومات');
      }
    } catch (e) {
      _error.value = 'خطأ في إضافة العنصر: $e';
      debugPrint('خطأ في إضافة العنصر: $e');
    }
  }

  /// تحديث موقع عنصر
  Future<void> updateWidgetPosition(dynamic widgetId, int row, int column) async {
    try {
      final dashboard = _currentDashboard.value;
      if (dashboard != null) {
        final updatedWidgets = (dashboard.dashboardWidgets ?? []).map((w) {
          if (w.id.toString() == widgetId) {
            return w.copyWith(positionX: column, positionY: row);
          }
          return w;
        }).toList();

        final updatedDashboard = dashboard.copyWith(
          dashboardWidgets: updatedWidgets,
          updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        );
        await updateDashboard(updatedDashboard);
      }
    } catch (e) {
      _error.value = 'خطأ في تحديث موقع العنصر: $e';
      debugPrint('خطأ في تحديث موقع العنصر: $e');
    }
  }

  /// تحديث إعدادات عنصر
  Future<void> updateWidgetSettings(dynamic widgetId, Map<String, dynamic> settings) async {
    try {
      final dashboard = _currentDashboard.value;
      if (dashboard != null) {
        final updatedWidgets = (dashboard.dashboardWidgets ?? []).map((w) {
          if (w.id.toString() == widgetId) {
            return w.copyWith(config: jsonEncode(settings));
          }
          return w;
        }).toList();

        final updatedDashboard = dashboard.copyWith(
          dashboardWidgets: updatedWidgets,
          updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        );
        await updateDashboard(updatedDashboard);
      }
    } catch (e) {
      _error.value = 'خطأ في تحديث إعدادات العنصر: $e';
      debugPrint('خطأ في تحديث إعدادات العنصر: $e');
    }
  }

  /// حفظ لوحة معلومات
  Future<void> saveDashboard(dashboard_models.Dashboard dashboard) async {
    await updateDashboard(dashboard);
  }

  /// تحديث معلومات لوحة المعلومات
  Future<bool> updateDashboardInfo(String title, String? description) async {
    try {
      final dashboard = _currentDashboard.value;
      if (dashboard != null) {
        final updatedDashboard = dashboard.copyWith(
          title: title,
          description: description ?? '',
          updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        );
        _currentDashboard.value = updatedDashboard;
        await _saveDashboards();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في تحديث معلومات لوحة المعلومات: $e');
      return false;
    }
  }

  /// تحديث أبعاد الشبكة
  Future<bool> updateGridDimensions(int rows, int columns) async {
    try {
      final dashboard = _currentDashboard.value;
      if (dashboard != null) {
        final updatedDashboard = dashboard.copyWith(
          gridRows: rows,
          gridColumns: columns,
          updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        );
        _currentDashboard.value = updatedDashboard;
        await _saveDashboards();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في تحديث أبعاد الشبكة: $e');
      return false;
    }
  }

  /// حفظ جميع لوحات المعلومات
  Future<void> _saveDashboards() async {
    try {
      // TODO: تنفيذ حفظ لوحات المعلومات في StorageService
      debugPrint('تم حفظ ${_dashboards.length} لوحة معلومات');
    } catch (e) {
      debugPrint('خطأ في حفظ لوحات المعلومات: $e');
    }
  }

  /// إنشاء لوحة معلومات افتراضية
  Future<void> createDefaultDashboard() async {
    if (_dashboards.isEmpty) {
      await createDashboard(
        title: 'لوحة المعلومات الرئيسية',
        description: 'لوحة المعلومات الافتراضية',
        widgets: _getDefaultWidgets(),
      );
    }
  }

  /// الحصول على العناصر الافتراضية
  List<dashboard_models.DashboardWidget> _getDefaultWidgets() {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return [
      dashboard_models.DashboardWidget(
        id: 1,
        dashboardId: 1,
        type: 'chart',
        title: 'ملخص المهام',
        positionX: 0,
        positionY: 0,
        width: 2,
        height: 1,
        config: jsonEncode({'chartType': 'pie', 'dataSource': 'tasks'}),
        createdAt: now,
      ),
      dashboard_models.DashboardWidget(
        id: 2,
        dashboardId: 1,
        type: 'list',
        title: 'الأنشطة الحديثة',
        positionX: 2,
        positionY: 0,
        width: 2,
        height: 2,
        config: jsonEncode({'dataSource': 'activities', 'limit': 10}),
        createdAt: now,
      ),
    ];
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }
}
