import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/message_attachments_controller.dart';
import '../services/api/message_attachments_api_service.dart';

/// صفحة اختبار نظام مرفقات الرسائل
class MessageAttachmentsTestScreen extends StatefulWidget {
  const MessageAttachmentsTestScreen({super.key});

  @override
  State<MessageAttachmentsTestScreen> createState() => _MessageAttachmentsTestScreenState();
}

class _MessageAttachmentsTestScreenState extends State<MessageAttachmentsTestScreen> {
  final _attachmentsController = Get.put(MessageAttachmentsController());
  final _testResults = <String>[].obs;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار نظام مرفقات الرسائل'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // معلومات النظام
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'حالة النظام',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Obx(() => Text(
                      'حالة التحميل: ${_attachmentsController.isLoading ? "جاري التحميل..." : "جاهز"}',
                    )),
                    Obx(() => Text(
                      'الأخطاء: ${_attachmentsController.error.isEmpty ? "لا توجد أخطاء" : _attachmentsController.error}',
                      style: TextStyle(
                        color: _attachmentsController.error.isEmpty ? Colors.green : Colors.red,
                      ),
                    )),
                    Obx(() => Text(
                      'عدد المرفقات المحملة: ${_attachmentsController.allAttachments.length}',
                    )),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // أزرار الاختبار
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'اختبارات النظام',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // اختبار تحميل المرفقات
                    ElevatedButton(
                      onPressed: _testLoadAttachments,
                      child: const Text('اختبار تحميل المرفقات'),
                    ),
                    const SizedBox(height: 8),
                    
                    // اختبار API Service
                    ElevatedButton(
                      onPressed: _testApiService,
                      child: const Text('اختبار API Service'),
                    ),
                    const SizedBox(height: 8),
                    
                    // اختبار رفع ملف وهمي
                    ElevatedButton(
                      onPressed: _testUploadDummyFile,
                      child: const Text('اختبار رفع ملف وهمي'),
                    ),
                    const SizedBox(height: 8),
                    
                    // مسح النتائج
                    ElevatedButton(
                      onPressed: _clearResults,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey,
                      ),
                      child: const Text('مسح النتائج'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // نتائج الاختبار
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'نتائج الاختبار',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: Obx(() => ListView.builder(
                          itemCount: _testResults.length,
                          itemBuilder: (context, index) {
                            final result = _testResults[index];
                            final isError = result.startsWith('❌');
                            final isSuccess = result.startsWith('✅');
                            
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2),
                              child: Text(
                                result,
                                style: TextStyle(
                                  color: isError ? Colors.red : 
                                         isSuccess ? Colors.green : 
                                         Colors.black87,
                                  fontFamily: 'monospace',
                                ),
                              ),
                            );
                          },
                        )),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// اختبار تحميل المرفقات
  Future<void> _testLoadAttachments() async {
    _addResult('🔄 بدء اختبار تحميل المرفقات...');
    
    try {
      await _attachmentsController.loadAllAttachments();
      _addResult('✅ تم تحميل المرفقات بنجاح');
      _addResult('📊 عدد المرفقات: ${_attachmentsController.allAttachments.length}');
    } catch (e) {
      _addResult('❌ فشل في تحميل المرفقات: $e');
    }
  }

  /// اختبار API Service
  Future<void> _testApiService() async {
    _addResult('🔄 بدء اختبار API Service...');
    
    try {
      final apiService = MessageAttachmentsApiService();
      _addResult('✅ تم إنشاء API Service بنجاح');
      
      // اختبار الحصول على جميع المرفقات
      final attachments = await apiService.getAllAttachments();
      _addResult('✅ تم استدعاء getAllAttachments بنجاح');
      _addResult('📊 عدد المرفقات من API: ${attachments.length}');
      
    } catch (e) {
      _addResult('❌ فشل في اختبار API Service: $e');
    }
  }

  /// اختبار رفع ملف وهمي
  Future<void> _testUploadDummyFile() async {
    _addResult('🔄 بدء اختبار رفع ملف وهمي...');
    
    try {
      // إنشاء ملف نصي مؤقت
      final tempDir = Directory.systemTemp;
      final testFile = File('${tempDir.path}/test_attachment.txt');
      await testFile.writeAsString('هذا ملف اختبار لنظام المرفقات\nتاريخ الإنشاء: ${DateTime.now()}');
      
      _addResult('✅ تم إنشاء ملف الاختبار: ${testFile.path}');
      
      // محاولة رفع الملف
      final success = await _attachmentsController.uploadAttachmentFile(
        file: testFile,
        messageId: 1, // معرف رسالة وهمي
        uploadedBy: 1, // معرف مستخدم وهمي
        description: 'ملف اختبار من التطبيق',
      );
      
      if (success) {
        _addResult('✅ تم رفع الملف بنجاح!');
      } else {
        _addResult('❌ فشل في رفع الملف');
      }
      
      // حذف الملف المؤقت
      await testFile.delete();
      _addResult('🗑️ تم حذف ملف الاختبار');
      
    } catch (e) {
      _addResult('❌ فشل في اختبار رفع الملف: $e');
    }
  }

  /// إضافة نتيجة اختبار
  void _addResult(String result) {
    _testResults.add('${DateTime.now().toString().substring(11, 19)} - $result');
  }

  /// مسح النتائج
  void _clearResults() {
    _testResults.clear();
    _addResult('🧹 تم مسح النتائج');
  }
}
