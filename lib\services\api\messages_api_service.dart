import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/models/message_models.dart';

import 'api_service.dart';

/// خدمة API للرسائل - متطابقة مع ASP.NET Core API
class MessagesApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع الرسائل
  Future<List<Message>> getAllMessages() async {
    try {
      final response = await _apiService.get('/api/Messages');
      return _apiService.handleListResponse<Message>(
        response,
        (json) => Message.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الرسائل: $e');
      rethrow;
    }
  }

  /// الحصول على رسالة بواسطة المعرف
  Future<Message?> getMessageById(int id) async {
    try {
      final response = await _apiService.get('/api/Messages/$id');
      return _apiService.handleResponse<Message>(
        response,
        (json) => Message.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الرسالة $id: $e');
      return null;
    }
  }

  /// الحصول على رسائل مجموعة محددة
  Future<List<Message>> getGroupMessages(int groupId) async {
    try {
      final response = await _apiService.get('/api/Messages/group/$groupId');
      return _apiService.handleListResponse<Message>(
        response,
        (json) => Message.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على رسائل المجموعة $groupId: $e');
      rethrow;
    }
  }

  /// الحصول على الرسائل غير المقروءة
  Future<List<Message>> getUnreadMessages() async {
    try {
      final response = await _apiService.get('/api/Messages/unread');
      return _apiService.handleListResponse<Message>(
        response,
        (json) => Message.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الرسائل غير المقروءة: $e');
      rethrow;
    }
  }

  /// الحصول على رسائل مستخدم محدد
  Future<List<Message>> getUserMessages(int userId) async {
    try {
      final response = await _apiService.get('/api/Messages/user/$userId');
      return _apiService.handleListResponse<Message>(
        response,
        (json) => Message.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على رسائل المستخدم $userId: $e');
      rethrow;
    }
  }

  /// الحصول على الرسائل الحديثة
  Future<List<Message>> getRecentMessages({int limit = 50}) async {
    try {
      final response = await _apiService.get('/api/Messages/recent?limit=$limit');
      return _apiService.handleListResponse<Message>(
        response,
        (json) => Message.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الرسائل الحديثة: $e');
      return [];
    }
  }

  /// إرسال رسالة جديدة
  Future<Message> sendMessage(Message message) async {
    try {
      final response = await _apiService.post(
        '/api/Messages',
        message.toJson(),
      );
      return _apiService.handleResponse<Message>(
        response,
        (json) => Message.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إرسال الرسالة: $e');
      rethrow;
    }
  }

  /// تحديث رسالة
  Future<Message> updateMessage(int id, Message message) async {
    try {
      final response = await _apiService.put(
        '/api/Messages/$id',
        message.toJson(),
      );
      return _apiService.handleResponse<Message>(
        response,
        (json) => Message.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث الرسالة $id: $e');
      rethrow;
    }
  }

  /// حذف رسالة
  Future<bool> deleteMessage(int id) async {
    try {
      final response = await _apiService.delete('/api/Messages/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف الرسالة $id: $e');
      return false;
    }
  }

  /// تحديد رسالة كمقروءة
  Future<bool> markMessageAsRead(int id) async {
    try {
      final response = await _apiService.put(
        '/api/Messages/$id/mark-read',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديد الرسالة كمقروءة: $e');
      return false;
    }
  }

  /// تحديد جميع رسائل مجموعة كمقروءة
  Future<bool> markGroupMessagesAsRead(int groupId) async {
    try {
      final response = await _apiService.put(
        '/api/Messages/group/$groupId/mark-all-read',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديد رسائل المجموعة كمقروءة: $e');
      return false;
    }
  }

  /// تحديد جميع رسائل مجموعة كمقروءة لمستخدم محدد
  Future<bool> markAllGroupMessagesAsRead(int groupId, int userId) async {
    try {
      final response = await _apiService.put(
        '/api/Messages/group/$groupId/mark-all-read?userId=$userId',
        {},
      );

      debugPrint('📬 تحديد رسائل المجموعة $groupId كمقروءة للمستخدم $userId - Status: ${response.statusCode}');

      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('❌ خطأ في تحديد رسائل المجموعة كمقروءة: $e');
      return false;
    }
  }

  /// البحث في الرسائل
  Future<List<Message>> searchMessages(String query) async {
    try {
      final response = await _apiService.get('/api/Messages/search?q=$query');
      return _apiService.handleListResponse<Message>(
        response,
        (json) => Message.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في الرسائل: $e');
      return [];
    }
  }

  /// البحث في رسائل مجموعة محددة
  Future<List<Message>> searchGroupMessages(int groupId, String query) async {
    try {
      final response = await _apiService.get('/api/Messages/group/$groupId/search?q=$query');
      return _apiService.handleListResponse<Message>(
        response,
        (json) => Message.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في رسائل المجموعة: $e');
      return [];
    }
  }

  /// الحصول على الرسائل بحسب التاريخ
  Future<List<Message>> getMessagesByDateRange(
    int groupId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final startTimestamp = startDate.millisecondsSinceEpoch ~/ 1000;
      final endTimestamp = endDate.millisecondsSinceEpoch ~/ 1000;
      
      final response = await _apiService.get(
        '/api/Messages/group/$groupId/date-range?start=$startTimestamp&end=$endTimestamp',
      );
      return _apiService.handleListResponse<Message>(
        response,
        (json) => Message.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الرسائل بحسب التاريخ: $e');
      return [];
    }
  }

  /// الحصول على عدد الرسائل غير المقروءة
  Future<int> getUnreadMessageCount() async {
    try {
      final response = await _apiService.get('/api/Messages/unread-count');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['count'] as int? ?? 0;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد الرسائل غير المقروءة: $e');
      return 0;
    }
  }

  /// الحصول على عدد الرسائل غير المقروءة لمجموعة محددة
  Future<int> getGroupUnreadMessageCount(int groupId) async {
    try {
      final response = await _apiService.get('/api/Messages/group/$groupId/unread-count');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['count'] as int? ?? 0;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد الرسائل غير المقروءة للمجموعة: $e');
      return 0;
    }
  }

  /// تثبيت رسالة
  Future<bool> pinMessage(int id, int userId) async {
    try {
      final response = await _apiService.patch(
        '/api/Messages/$id/pin/$userId',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تثبيت الرسالة: $e');
      return false;
    }
  }

  /// إلغاء تثبيت رسالة
  Future<bool> unpinMessage(int id, int userId) async {
    try {
      // الحصول على الرسالة أولاً
      final message = await getMessageById(id);
      if (message == null) {
        return false;
      }

      // تحديث الرسالة لإلغاء التثبيت
      final updatedMessage = message.copyWith(
        isPinned: false,
        pinnedBy: null,
        pinnedAt: null,
      );

      final response = await _apiService.put(
        '/api/Messages/$id',
        updatedMessage.toJson(),
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء تثبيت الرسالة: $e');
      return false;
    }
  }

  /// الحصول على الرسائل المثبتة لمجموعة
  Future<List<Message>> getPinnedMessages(int groupId) async {
    try {
      // الحصول على جميع رسائل المجموعة
      final allMessages = await getGroupMessages(groupId);

      // تصفية الرسائل المثبتة فقط
      final pinnedMessages = allMessages.where((message) => message.isPinned).toList();

      // ترتيب الرسائل حسب تاريخ التثبيت (الأحدث أولاً)
      pinnedMessages.sort((a, b) {
        final aPinnedAt = a.pinnedAt ?? a.createdAt;
        final bPinnedAt = b.pinnedAt ?? b.createdAt;
        return bPinnedAt.compareTo(aPinnedAt);
      });

      return pinnedMessages;
    } catch (e) {
      debugPrint('خطأ في الحصول على الرسائل المثبتة: $e');
      return [];
    }
  }

  /// الرد على رسالة
  Future<Message> replyToMessage(int messageId, Message reply) async {
    try {
      final response = await _apiService.post(
        '/api/Messages/$messageId/reply',
        reply.toJson(),
      );
      return _apiService.handleResponse<Message>(
        response,
        (json) => Message.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الرد على الرسالة: $e');
      rethrow;
    }
  }

  /// إعادة توجيه رسالة
  Future<Message> forwardMessage(int messageId, int toGroupId) async {
    try {
      final response = await _apiService.post(
        '/api/Messages/$messageId/forward',
        {
          'toGroupId': toGroupId,
        },
      );
      return _apiService.handleResponse<Message>(
        response,
        (json) => Message.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إعادة توجيه الرسالة: $e');
      rethrow;
    }
  }

  /// الحصول على إحصائيات الرسائل
  Future<Map<String, dynamic>> getMessageStatistics() async {
    try {
      final response = await _apiService.get('/api/Messages/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات الرسائل: $e');
      return {};
    }
  }

  /// حذف جميع رسائل مجموعة
  Future<bool> deleteGroupMessages(int groupId) async {
    try {
      final response = await _apiService.delete('/api/Messages/group/$groupId');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف رسائل المجموعة: $e');
      return false;
    }
  }
}
