{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["tA3Szi388WMMBn+290KJ6k3Ps1Etsx5SJyIFbroQ7x8=", "NwCWSOnmsB1O0sJ8Luel6odfHuKOvElOiZd/7h620Yo=", "Bfj/1Ni28r25vgE5lYEm6Wf6GlfzC8CYEWdNQJ3+6F0="], "CachedAssets": {"Bfj/1Ni28r25vgE5lYEm6Wf6GlfzC8CYEWdNQJ3+6F0=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\v25brddiup-knc7nr4hqg.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e#[.{fingerprint=knc7nr4hqg}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "72uezgg7hx", "Integrity": "UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "FileLength": 1927, "LastWriteTime": "2025-07-14T01:06:36.4000198+00:00"}, "NwCWSOnmsB1O0sJ8Luel6odfHuKOvElOiZd/7h620Yo=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ph3rqhnt6w-knc7nr4hqg.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca#[.{fingerprint=knc7nr4hqg}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "72uezgg7hx", "Integrity": "UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "FileLength": 1927, "LastWriteTime": "2025-07-14T01:06:36.4000198+00:00"}, "tA3Szi388WMMBn+290KJ6k3Ps1Etsx5SJyIFbroQ7x8=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\xoelc6sdnq-dz1rjozczi.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2)#[.{fingerprint=dz1rjo<PERSON>cz<PERSON>}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "00icdbvmee", "Integrity": "II89AIAfepVyxMAEBSxXRGF+cpbNgb15nTDmDlReLDI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "FileLength": 7549, "LastWriteTime": "2025-07-14T01:06:36.4010198+00:00"}}, "CachedCopyCandidates": {}}