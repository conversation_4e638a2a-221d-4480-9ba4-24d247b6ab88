import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/unified_chat_controller.dart';
import '../controllers/auth_controller.dart';
import '../services/api/messages_api_service.dart';
import '../helpers/chat_list_diagnostic.dart';

/// شاشة اختبار نظام الرسائل غير المقروءة
class UnreadMessagesTestScreen extends StatefulWidget {
  const UnreadMessagesTestScreen({super.key});

  @override
  State<UnreadMessagesTestScreen> createState() => _UnreadMessagesTestScreenState();
}

class _UnreadMessagesTestScreenState extends State<UnreadMessagesTestScreen> {
  final _chatController = Get.find<UnifiedChatController>();
  final _authController = Get.find<AuthController>();
  final _testResults = <String>[].obs;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار الرسائل غير المقروءة'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // معلومات النظام
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'حالة النظام',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Obx(() => Text(
                      'المستخدم الحالي: ${_authController.currentUser.value?.name ?? "غير مسجل"}',
                    )),
                    Obx(() => Text(
                      'عدد المجموعات: ${_chatController.chatGroups.length}',
                    )),
                    Obx(() => Text(
                      'حالة التحميل: ${_chatController.isLoading ? "جاري التحميل..." : "جاهز"}',
                    )),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // أزرار الاختبار
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'اختبارات النظام',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // اختبار تحميل المجموعات
                    ElevatedButton(
                      onPressed: _testLoadGroups,
                      child: const Text('اختبار تحميل المجموعات'),
                    ),
                    const SizedBox(height: 8),
                    
                    // اختبار عدد الرسائل غير المقروءة
                    ElevatedButton(
                      onPressed: _testUnreadCounts,
                      child: const Text('اختبار عدد الرسائل غير المقروءة'),
                    ),
                    const SizedBox(height: 8),
                    
                    // اختبار تحديد رسائل كمقروءة
                    ElevatedButton(
                      onPressed: _testMarkAsRead,
                      child: const Text('اختبار تحديد رسائل كمقروءة'),
                    ),
                    const SizedBox(height: 8),
                    
                    // تشخيص شامل
                    ElevatedButton(
                      onPressed: _runFullDiagnostic,
                      child: const Text('تشخيص شامل'),
                    ),
                    const SizedBox(height: 8),
                    
                    // مسح النتائج
                    ElevatedButton(
                      onPressed: _clearResults,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey,
                      ),
                      child: const Text('مسح النتائج'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // نتائج الاختبار
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'نتائج الاختبار',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: Obx(() => ListView.builder(
                          itemCount: _testResults.length,
                          itemBuilder: (context, index) {
                            final result = _testResults[index];
                            final isError = result.startsWith('❌');
                            final isSuccess = result.startsWith('✅');
                            final isWarning = result.startsWith('⚠️');
                            
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2),
                              child: Text(
                                result,
                                style: TextStyle(
                                  color: isError ? Colors.red : 
                                         isSuccess ? Colors.green :
                                         isWarning ? Colors.orange :
                                         Colors.black87,
                                  fontFamily: 'monospace',
                                  fontSize: 12,
                                ),
                              ),
                            );
                          },
                        )),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// اختبار تحميل المجموعات
  Future<void> _testLoadGroups() async {
    _addResult('🔄 بدء اختبار تحميل المجموعات...');
    
    try {
      await _chatController.loadChatGroups();
      _addResult('✅ تم تحميل المجموعات بنجاح');
      _addResult('📊 عدد المجموعات: ${_chatController.chatGroups.length}');
      
      // عرض تفاصيل أول 3 مجموعات
      for (int i = 0; i < _chatController.chatGroups.length && i < 3; i++) {
        final group = _chatController.chatGroups[i];
        _addResult('  ${i + 1}. ${group.name}');
        _addResult('     👥 أعضاء: ${group.memberCount}');
        _addResult('     📬 غير مقروءة: ${group.unreadCount}');
      }
      
    } catch (e) {
      _addResult('❌ فشل في تحميل المجموعات: $e');
    }
  }

  /// اختبار عدد الرسائل غير المقروءة
  Future<void> _testUnreadCounts() async {
    _addResult('🔄 بدء اختبار عدد الرسائل غير المقروءة...');
    
    try {
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) {
        _addResult('❌ لا يوجد مستخدم مسجل');
        return;
      }
      
      // اختبار API مباشرة
      final messagesApi = MessagesApiService();
      final totalUnread = await messagesApi.getUnreadMessageCount(currentUser.id);
      _addResult('✅ إجمالي الرسائل غير المقروءة من API: $totalUnread');
      
      // فحص كل مجموعة
      int frontendTotal = 0;
      for (final group in _chatController.chatGroups) {
        frontendTotal += group.unreadCount;
        if (group.unreadCount > 0) {
          _addResult('📬 ${group.name}: ${group.unreadCount} رسائل غير مقروءة');
        }
      }
      
      _addResult('📊 إجمالي الرسائل غير المقروءة في Frontend: $frontendTotal');
      
      if (totalUnread != frontendTotal) {
        _addResult('⚠️ تحذير: عدم تطابق بين Backend ($totalUnread) و Frontend ($frontendTotal)');
      } else {
        _addResult('✅ تطابق الأعداد بين Backend و Frontend');
      }
      
    } catch (e) {
      _addResult('❌ فشل في اختبار عدد الرسائل غير المقروءة: $e');
    }
  }

  /// اختبار تحديد رسائل كمقروءة
  Future<void> _testMarkAsRead() async {
    _addResult('🔄 بدء اختبار تحديد رسائل كمقروءة...');
    
    try {
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) {
        _addResult('❌ لا يوجد مستخدم مسجل');
        return;
      }
      
      // البحث عن مجموعة بها رسائل غير مقروءة
      final groupWithUnread = _chatController.chatGroups
          .where((g) => g.unreadCount > 0)
          .firstOrNull;
      
      if (groupWithUnread == null) {
        _addResult('ℹ️ لا توجد مجموعات بها رسائل غير مقروءة للاختبار');
        return;
      }
      
      _addResult('📋 اختبار على مجموعة: ${groupWithUnread.name}');
      _addResult('📬 رسائل غير مقروءة قبل التحديد: ${groupWithUnread.unreadCount}');
      
      // تحديد جميع الرسائل كمقروءة
      final success = await _chatController.markAllGroupMessagesAsRead(
        groupWithUnread.id,
        currentUser.id,
      );
      
      if (success) {
        _addResult('✅ تم تحديد الرسائل كمقروءة بنجاح');
        
        // انتظار قليل ثم إعادة تحميل
        await Future.delayed(const Duration(seconds: 2));
        await _chatController.loadChatGroups();
        
        final updatedGroup = _chatController.chatGroups
            .where((g) => g.id == groupWithUnread.id)
            .firstOrNull;
        
        if (updatedGroup != null) {
          _addResult('📬 رسائل غير مقروءة بعد التحديد: ${updatedGroup.unreadCount}');
          
          if (updatedGroup.unreadCount == 0) {
            _addResult('✅ تم تحديث العدد بنجاح');
          } else {
            _addResult('⚠️ العدد لم يتحدث كما متوقع');
          }
        }
      } else {
        _addResult('❌ فشل في تحديد الرسائل كمقروءة');
      }
      
    } catch (e) {
      _addResult('❌ فشل في اختبار تحديد الرسائل: $e');
    }
  }

  /// تشخيص شامل
  Future<void> _runFullDiagnostic() async {
    _addResult('🔍 بدء التشخيص الشامل...');
    
    try {
      await ChatListDiagnostic.runFullDiagnostic();
      _addResult('✅ تم إكمال التشخيص الشامل - راجع console للتفاصيل');
    } catch (e) {
      _addResult('❌ فشل في التشخيص الشامل: $e');
    }
  }

  /// إضافة نتيجة اختبار
  void _addResult(String result) {
    _testResults.add('${DateTime.now().toString().substring(11, 19)} - $result');
  }

  /// مسح النتائج
  void _clearResults() {
    _testResults.clear();
    _addResult('🧹 تم مسح النتائج');
  }
}
