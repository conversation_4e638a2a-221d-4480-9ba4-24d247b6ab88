import 'package:flutter/foundation.dart';
import 'user_model.dart';
import 'chat_models.dart';

/// أدوار أعضاء المجموعة
enum GroupMemberRole {
  member(1, 'عضو'),
  moderator(2, 'مشرف'),
  admin(3, 'مدير');

  const GroupMemberRole(this.value, this.displayName);
  
  final int value;
  final String displayName;

  static GroupMemberRole fromValue(int value) {
    return GroupMemberRole.values.firstWhere(
      (role) => role.value == value,
      orElse: () => GroupMemberRole.member,
    );
  }
}

/// نموذج عضو المجموعة - متطابق مع ASP.NET Core API
class GroupMember {
  final int id;
  final int groupId;
  final int userId;
  final int role; // int في API: 1=member, 2=moderator, 3=admin
  final int joinedAt; // long في API
  final bool isDeleted;
  final int? leftAt; // long في API

  // Navigation properties
  final ChatGroup? group;
  final User? user;

  const GroupMember({
    required this.id,
    required this.groupId,
    required this.userId,
    this.role = 1, // 1 = member (default)
    required this.joinedAt,
    this.isDeleted = false,
    this.leftAt,
    this.group,
    this.user,
  });

  factory GroupMember.fromJson(Map<String, dynamic> json) {
    debugPrint('تحويل GroupMember من JSON: ${json.toString()}');

    // ✅ معالجة التنسيق القديم والجديد للبيانات
    User? user;
    if (json['user'] != null) {
      // التنسيق الجديد - كائن user كامل
      user = User.fromJson(json['user'] as Map<String, dynamic>);
    } else if (json['userName'] != null) {
      // التنسيق القديم - خصائص منفصلة
      user = User(
        id: json['userId'] as int,
        name: json['userName'] as String? ?? 'مستخدم غير معروف',
        email: json['userEmail'] as String?,
        profileImage: json['userImageUrl'] as String?,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        isActive: true,
        isOnline: false,
      );
    }

    return GroupMember(
      id: json['id'] as int,
      groupId: json['groupId'] as int,
      userId: json['userId'] as int,
      role: json['role'] as int? ?? 1,
      joinedAt: json['joinedAt'] as int,
      isDeleted: json['isDeleted'] as bool? ?? false,
      leftAt: json['leftAt'] as int?,
      group: json['group'] != null
          ? ChatGroup.fromJson(json['group'] as Map<String, dynamic>)
          : null,
      user: user,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'groupId': groupId,
      'userId': userId,
      'role': role,
      'joinedAt': joinedAt,
      'isDeleted': isDeleted,
      'leftAt': leftAt,
    };
  }

  GroupMember copyWith({
    int? id,
    int? groupId,
    int? userId,
    int? role,
    int? joinedAt,
    bool? isDeleted,
    int? leftAt,
    ChatGroup? group,
    User? user,
  }) {
    return GroupMember(
      id: id ?? this.id,
      groupId: groupId ?? this.groupId,
      userId: userId ?? this.userId,
      role: role ?? this.role,
      joinedAt: joinedAt ?? this.joinedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      leftAt: leftAt ?? this.leftAt,
      group: group ?? this.group,
      user: user ?? this.user,
    );
  }

  /// الحصول على دور العضو كـ enum
  GroupMemberRole get memberRole => GroupMemberRole.fromValue(role);

  /// الحصول على تاريخ الانضمام كـ DateTime
  DateTime get joinedAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(joinedAt * 1000);

  /// الحصول على تاريخ المغادرة كـ DateTime
  DateTime? get leftAtDateTime => leftAt != null
      ? DateTime.fromMillisecondsSinceEpoch(leftAt! * 1000)
      : null;

  /// التحقق من كون العضو مدير
  bool get isAdmin => role == GroupMemberRole.admin.value;

  /// التحقق من كون العضو مشرف
  bool get isModerator => role == GroupMemberRole.moderator.value;

  /// التحقق من كون العضو عادي
  bool get isRegularMember => role == GroupMemberRole.member.value;

  /// التحقق من كون العضو نشط (لم يغادر ولم يُحذف)
  bool get isActive => !isDeleted && leftAt == null;

  /// التحقق من صلاحيات الإدارة (مدير أو مشرف)
  bool get hasAdminPermissions => isAdmin || isModerator;

  /// الحصول على اسم الدور
  String get roleDisplayName => memberRole.displayName;

  @override
  String toString() {
    return 'GroupMember(id: $id, userId: $userId, role: $roleDisplayName, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GroupMember && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إضافة عضو للمجموعة
class AddGroupMemberRequest {
  final int groupId;
  final int userId;
  final int role;

  const AddGroupMemberRequest({
    required this.groupId,
    required this.userId,
    this.role = 1, // member by default
  });

  Map<String, dynamic> toJson() {
    return {
      'groupId': groupId,
      'userId': userId,
      'role': role,
    };
  }
}

/// نموذج طلب تحديث دور العضو
class UpdateGroupMemberRoleRequest {
  final int memberId;
  final int newRole;

  const UpdateGroupMemberRoleRequest({
    required this.memberId,
    required this.newRole,
  });

  Map<String, dynamic> toJson() {
    return {
      'memberId': memberId,
      'newRole': newRole,
    };
  }
}

/// نموذج طلب إزالة عضو من المجموعة
class RemoveGroupMemberRequest {
  final int groupId;
  final int userId;
  final String? reason;

  const RemoveGroupMemberRequest({
    required this.groupId,
    required this.userId,
    this.reason,
  });

  Map<String, dynamic> toJson() {
    return {
      'groupId': groupId,
      'userId': userId,
      'reason': reason,
    };
  }
}

/// إحصائيات أعضاء المجموعة
class GroupMemberStatistics {
  final int groupId;
  final int totalMembers;
  final int activeMembers;
  final int adminCount;
  final int moderatorCount;
  final int memberCount;
  final int leftMembers;
  final DateTime? lastJoinDate;
  final DateTime? lastLeaveDate;

  const GroupMemberStatistics({
    required this.groupId,
    required this.totalMembers,
    required this.activeMembers,
    required this.adminCount,
    required this.moderatorCount,
    required this.memberCount,
    required this.leftMembers,
    this.lastJoinDate,
    this.lastLeaveDate,
  });

  factory GroupMemberStatistics.fromJson(Map<String, dynamic> json) {
    return GroupMemberStatistics(
      groupId: json['groupId'] as int,
      totalMembers: json['totalMembers'] as int,
      activeMembers: json['activeMembers'] as int,
      adminCount: json['adminCount'] as int,
      moderatorCount: json['moderatorCount'] as int,
      memberCount: json['memberCount'] as int,
      leftMembers: json['leftMembers'] as int,
      lastJoinDate: json['lastJoinDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch((json['lastJoinDate'] as int) * 1000)
          : null,
      lastLeaveDate: json['lastLeaveDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch((json['lastLeaveDate'] as int) * 1000)
          : null,
    );
  }

  /// نسبة الأعضاء النشطين
  double get activePercentage => totalMembers > 0 ? (activeMembers / totalMembers) * 100 : 0;

  /// نسبة المديرين والمشرفين
  double get adminPercentage => totalMembers > 0 ? ((adminCount + moderatorCount) / totalMembers) * 100 : 0;

  @override
  String toString() {
    return 'GroupMemberStatistics(groupId: $groupId, total: $totalMembers, active: $activeMembers, admins: $adminCount)';
  }
}

/// نموذج استجابة عضوية المجموعة
class GroupMembershipResponse {
  final bool success;
  final String? message;
  final GroupMember? member;
  final GroupMemberStatistics? statistics;
  final String? error;

  const GroupMembershipResponse({
    required this.success,
    this.message,
    this.member,
    this.statistics,
    this.error,
  });

  factory GroupMembershipResponse.fromJson(Map<String, dynamic> json) {
    return GroupMembershipResponse(
      success: json['success'] as bool,
      message: json['message'] as String?,
      member: json['member'] != null
          ? GroupMember.fromJson(json['member'] as Map<String, dynamic>)
          : null,
      statistics: json['statistics'] != null
          ? GroupMemberStatistics.fromJson(json['statistics'] as Map<String, dynamic>)
          : null,
      error: json['error'] as String?,
    );
  }
}
