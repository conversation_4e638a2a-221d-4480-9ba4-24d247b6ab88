using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Models.DTOs;
using webApi.Hubs;
using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace webApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize] // إضافة الحماية لجميع endpoints
    public class ChatGroupsController : ControllerBase
    {
        private readonly TasksDbContext _context;
        private readonly IHubContext<ChatHub> _chatHubContext;
        private readonly ILogger<ChatGroupsController> _logger;

        public ChatGroupsController(TasksDbContext context, IHubContext<ChatHub> chatHubContext, ILogger<ChatGroupsController> logger)
        {
            _context = context;
            _chatHubContext = chatHubContext;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على معرف المستخدم الحالي من JWT Token
        /// </summary>
        private int GetCurrentUserId()
        {
            try
            {
                // محاولة الحصول على معرف المستخدم من claims مختلفة
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value
                                ?? User.FindFirst("UserId")?.Value
                                ?? User.FindFirst("sub")?.Value;

                _logger.LogInformation("محاولة الحصول على معرف المستخدم من JWT. Claims متوفرة: {Claims}",
                    string.Join(", ", User.Claims.Select(c => $"{c.Type}={c.Value}")));

                if (int.TryParse(userIdClaim, out int userId))
                {
                    _logger.LogInformation("تم العثور على معرف المستخدم: {UserId}", userId);
                    return userId;
                }

                _logger.LogWarning("لم يتم العثور على معرف مستخدم صالح في JWT Token. Claims: {Claims}",
                    string.Join(", ", User.Claims.Select(c => $"{c.Type}={c.Value}")));

                throw new UnauthorizedAccessException("معرف المستخدم غير صالح في JWT Token");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على معرف المستخدم من JWT Token");
                throw new UnauthorizedAccessException("خطأ في معالجة JWT Token");
            }
        }

        /// <summary>
        /// التحقق من صلاحيات المستخدم في المجموعة
        /// </summary>
        private async Task<bool> HasGroupPermission(int groupId, int userId, int requiredRole = 1)
        {
            var member = await _context.GroupMembers
                .FirstOrDefaultAsync(m => m.GroupId == groupId && m.UserId == userId && !m.IsDeleted);

            return member != null && member.Role >= requiredRole;
        }

        // GET: api/ChatGroups
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ChatGroup>>> GetChatGroups()
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                _logger.LogInformation("Fetching chat groups for user {UserId}", currentUserId);

                // جلب المجموعات التي المستخدم عضو فيها فقط
                var userGroupIds = await _context.GroupMembers
                    .Where(m => m.UserId == currentUserId && !m.IsDeleted && m.LeftAt == null)
                    .Select(m => m.GroupId)
                    .ToListAsync();

                _logger.LogInformation("المستخدم {UserId} عضو في {Count} مجموعة: {GroupIds}",
                    currentUserId, userGroupIds.Count, string.Join(", ", userGroupIds));

                var chatGroups = await _context.ChatGroups
                    .Where(g => !g.IsArchived && !g.IsDeleted && userGroupIds.Contains(g.Id))
                    .OrderByDescending(g => g.CreatedAt)
                    .Select(g => new
                    {
                        g.Id,
                        g.Name,
                        g.Description,
                        g.IsPrivate,
                        g.CreatedBy,
                        g.CreatedAt,
                        g.UpdatedAt,
                        g.ImageUrl,
                        g.IsArchived,
                        g.IsDirectMessage,
                        g.GroupType,
                        g.MaxMembers,
                        g.IsDeleted,
                        // ✅ حساب عدد الأعضاء النشطين
                        MemberCount = _context.GroupMembers
                            .Count(m => m.GroupId == g.Id && !m.IsDeleted && m.LeftAt == null),
                        // ✅ حساب عدد الرسائل غير المقروءة للمستخدم الحالي
                        UnreadCount = _context.Messages
                            .Where(msg => msg.GroupId == g.Id && !msg.IsDeleted && msg.SenderId != currentUserId)
                            .Count() -
                            _context.MessageReads
                            .Where(mr => mr.UserId == currentUserId &&
                                   _context.Messages.Any(msg => msg.Id == mr.MessageId && msg.GroupId == g.Id))
                            .Count()
                    })
                    .ToListAsync();

                // تحويل البيانات إلى تنسيق ChatGroup مع المعلومات الإضافية
                var result = chatGroups.Select(g => new
                {
                    g.Id,
                    g.Name,
                    g.Description,
                    g.IsPrivate,
                    g.CreatedBy,
                    g.CreatedAt,
                    g.UpdatedAt,
                    g.ImageUrl,
                    g.IsArchived,
                    g.IsDirectMessage,
                    g.GroupType,
                    g.MaxMembers,
                    g.IsDeleted,
                    g.MemberCount,
                    g.UnreadCount
                }).ToList();

                _logger.LogInformation("Successfully retrieved {Count} chat groups for user {UserId}",
                    result.Count, currentUserId);

                // تسجيل تفصيلي للتشخيص
                _logger.LogInformation("المستخدم {UserId} عضو في {Count} مجموعة", currentUserId, result.Count);

                if (result.Any())
                {
                    var firstGroup = result.First();
                    _logger.LogInformation("عينة من بيانات المجموعة: Name={Name}, MemberCount={MemberCount}, UnreadCount={UnreadCount}",
                        firstGroup.Name, firstGroup.MemberCount, firstGroup.UnreadCount);

                    // تسجيل أسماء جميع المجموعات للتشخيص
                    var groupNames = string.Join(", ", result.Select(g => g.Name));
                    _logger.LogInformation("أسماء المجموعات للمستخدم {UserId}: {GroupNames}", currentUserId, groupNames);
                }

                return Ok(result);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized access to chat groups");
                return Unauthorized(new { message = "غير مصرح بالوصول" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting chat groups");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم", details = ex.Message });
            }
        }

        // GET: api/ChatGroups/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ChatGroup>> GetChatGroup(int id)
        {
            try
            {
                _logger.LogInformation("Fetching chat group {Id}", id);
                
                var chatGroup = await _context.ChatGroups
                    .Where(g => g.Id == id)
                    .Select(g => new ChatGroup
                    {
                        Id = g.Id,
                        Name = g.Name,
                        Description = g.Description,
                        IsPrivate = g.IsPrivate,
                        CreatedBy = g.CreatedBy,
                        CreatedAt = g.CreatedAt,
                        UpdatedAt = g.UpdatedAt,
                        ImageUrl = g.ImageUrl,
                        IsArchived = g.IsArchived,
                        IsDirectMessage = g.IsDirectMessage
                    })
                    .FirstOrDefaultAsync();

                if (chatGroup == null)
                {
                    _logger.LogWarning("Chat group {Id} not found", id);
                    return NotFound();
                }

                _logger.LogInformation("Successfully retrieved chat group {Id}", id);
                return Ok(chatGroup);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting chat group {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // POST: api/ChatGroups
        [HttpPost]
        public async Task<ActionResult<ChatGroup>> CreateChatGroup(CreateChatGroupRequest request)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                _logger.LogInformation("Creating chat group: {GroupName} by user {UserId}",
                    request.Name, currentUserId);

                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(request.Name))
                {
                    return BadRequest(new { message = "اسم المجموعة مطلوب" });
                }

                if (request.Name.Length > 100)
                {
                    return BadRequest(new { message = "اسم المجموعة يجب أن يكون أقل من 100 حرف" });
                }

                // التحقق من وجود المستخدم المنشئ
                var creatorExists = await _context.Users.AnyAsync(u => u.Id == currentUserId && !u.IsDeleted && u.IsActive);
                if (!creatorExists)
                {
                    return BadRequest(new { message = "المستخدم المنشئ غير موجود أو غير نشط" });
                }

                // إنشاء المجموعة
                var chatGroup = new ChatGroup
                {
                    Name = request.Name.Trim(),
                    Description = request.Description?.Trim(),
                    IsPrivate = request.IsPrivate,
                    IsDirectMessage = request.IsDirectMessage,
                    GroupType = request.GroupType ?? "general",
                    MaxMembers = request.MaxMembers,
                    CreatedBy = currentUserId,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    IsArchived = false,
                    IsDeleted = false
                };

                // استخدام ADO.NET مباشر لتجنب مشكلة Entity Framework
                using var connection = _context.Database.GetDbConnection();
                await connection.OpenAsync();

                using var command = connection.CreateCommand();
                command.CommandText = @"
                    INSERT INTO chat_groups (name, description, is_private, created_by, created_at, updated_at,
                                           image_url, is_archived, is_direct_message, group_type, max_members, is_deleted)
                    VALUES (@name, @description, @isPrivate, @createdBy, @createdAt, @updatedAt,
                            @imageUrl, @isArchived, @isDirectMessage, @groupType, @maxMembers, @isDeleted);
                    SELECT CAST(SCOPE_IDENTITY() AS INT);";

                // إضافة المعاملات
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@name", chatGroup.Name));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@description", chatGroup.Description ?? (object)DBNull.Value));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@isPrivate", chatGroup.IsPrivate));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@createdBy", chatGroup.CreatedBy));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@createdAt", chatGroup.CreatedAt));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@updatedAt", chatGroup.UpdatedAt ?? (object)DBNull.Value));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@imageUrl", chatGroup.ImageUrl ?? (object)DBNull.Value));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@isArchived", chatGroup.IsArchived));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@isDirectMessage", chatGroup.IsDirectMessage));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@groupType", chatGroup.GroupType));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@maxMembers", chatGroup.MaxMembers ?? (object)DBNull.Value));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@isDeleted", chatGroup.IsDeleted));

                var result = await command.ExecuteScalarAsync();
                chatGroup.Id = Convert.ToInt32(result);

                _logger.LogInformation("Chat group created with ID: {GroupId}", chatGroup.Id);

                // إضافة المنشئ كمدير باستخدام ADO.NET مباشر
                using var memberCommand = connection.CreateCommand();
                memberCommand.CommandText = @"
                    INSERT INTO group_members (group_id, user_id, role, joined_at, is_deleted)
                    VALUES (@groupId, @userId, @role, @joinedAt, @isDeleted)";

                memberCommand.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@groupId", chatGroup.Id));
                memberCommand.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@userId", currentUserId));
                memberCommand.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@role", 3)); // 3 = admin
                memberCommand.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@joinedAt", chatGroup.CreatedAt));
                memberCommand.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@isDeleted", false));

                await memberCommand.ExecuteNonQueryAsync();

                _logger.LogInformation("Creator added as admin member to group {GroupId}", chatGroup.Id);

                // إضافة الأعضاء المبدئيين إذا كانوا موجودين
                if (request.InitialMemberIds != null && request.InitialMemberIds.Any())
                {
                    _logger.LogInformation("Adding {Count} initial members to group {GroupId}",
                        request.InitialMemberIds.Count, chatGroup.Id);

                    foreach (var memberId in request.InitialMemberIds)
                    {
                        // تجنب إضافة المنشئ مرة أخرى
                        if (memberId == currentUserId)
                            continue;

                        // التحقق من وجود المستخدم
                        var memberExists = await _context.Users.AnyAsync(u => u.Id == memberId && !u.IsDeleted && u.IsActive);
                        if (!memberExists)
                        {
                            _logger.LogWarning("Initial member {MemberId} not found or inactive, skipping", memberId);
                            continue;
                        }

                        // التحقق من عدم وجود العضو مسبقاً
                        var existingMember = await _context.GroupMembers
                            .AnyAsync(m => m.GroupId == chatGroup.Id && m.UserId == memberId && !m.IsDeleted);

                        if (existingMember)
                        {
                            _logger.LogWarning("User {MemberId} is already a member of group {GroupId}, skipping",
                                memberId, chatGroup.Id);
                            continue;
                        }

                        // إضافة العضو باستخدام ADO.NET مباشر
                        using var initialMemberCommand = connection.CreateCommand();
                        initialMemberCommand.CommandText = @"
                            INSERT INTO group_members (group_id, user_id, role, joined_at, is_deleted)
                            VALUES (@groupId, @userId, @role, @joinedAt, @isDeleted)";

                        initialMemberCommand.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@groupId", chatGroup.Id));
                        initialMemberCommand.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@userId", memberId));
                        initialMemberCommand.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@role", 1)); // 1 = member
                        initialMemberCommand.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@joinedAt", chatGroup.CreatedAt));
                        initialMemberCommand.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@isDeleted", false));

                        await initialMemberCommand.ExecuteNonQueryAsync();
                    }

                    _logger.LogInformation("Initial members added to group {GroupId}", chatGroup.Id);
                }

                return CreatedAtAction(nameof(GetChatGroup), new { id = chatGroup.Id }, chatGroup);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized access to create chat group");
                return Unauthorized(new { message = "غير مصرح بإنشاء المجموعة" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating chat group: {GroupName}", request?.Name ?? "Unknown");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم", details = ex.Message });
            }
        }

        // PUT: api/ChatGroups/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateChatGroup(int id, ChatGroup chatGroup)
        {
            if (id != chatGroup.Id)
            {
                return BadRequest();
            }

            try
            {
                // Set update timestamp
                chatGroup.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                
                _context.Entry(chatGroup).State = EntityState.Modified;
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ChatGroupExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating chat group {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // DELETE: api/ChatGroups/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteChatGroup(int id)
        {
            try
            {
                var chatGroup = await _context.ChatGroups.FindAsync(id);
                if (chatGroup == null)
                {
                    return NotFound();
                }

                _context.ChatGroups.Remove(chatGroup);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting chat group {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/ChatGroups/user/5
        [HttpGet("user/{userId}")]
        public async Task<ActionResult<IEnumerable<ChatGroup>>> GetUserGroups(int userId)
        {
            try
            {
                _logger.LogInformation("Fetching groups for user {UserId}", userId);

                // Check if user exists and is active
                var userExists = await _context.Users.AnyAsync(u => u.Id == userId && !u.IsDeleted && u.IsActive);
                if (!userExists)
                {
                    _logger.LogWarning("User {UserId} not found or inactive", userId);

                    // إرجاع قائمة فارغة بدلاً من خطأ 404 لتجنب مشاكل UI
                    return Ok(new List<ChatGroup>());
                }

                // Use a more efficient query with joins
                var userGroups = await _context.GroupMembers
                    .Where(m => m.UserId == userId)
                    .Join(_context.ChatGroups.Where(g => !g.IsArchived),
                        m => m.GroupId,
                        g => g.Id,
                        (m, g) => g)
                    .OrderByDescending(g => g.CreatedAt)
                    .Select(g => new ChatGroup
                    {
                        Id = g.Id,
                        Name = g.Name,
                        Description = g.Description,
                        IsPrivate = g.IsPrivate,
                        CreatedBy = g.CreatedBy,
                        CreatedAt = g.CreatedAt,
                        UpdatedAt = g.UpdatedAt,
                        ImageUrl = g.ImageUrl,
                        IsArchived = g.IsArchived,
                        IsDirectMessage = g.IsDirectMessage
                    })
                    .ToListAsync();

                _logger.LogInformation("Successfully retrieved {Count} groups for user {UserId}",
                    userGroups.Count, userId);
                return Ok(userGroups);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user groups for user {UserId}", userId);
                // إرجاع قائمة فارغة في حالة الخطأ لتجنب crash في UI
                return Ok(new List<ChatGroup>());
            }
        }

        // GET: api/ChatGroups/search?searchTerm=term
        [HttpGet("search")]
        public async Task<ActionResult<IEnumerable<ChatGroup>>> SearchGroups([FromQuery] string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return BadRequest("Search term is required");
                }

                _logger.LogInformation("Searching groups with term: {SearchTerm}", searchTerm);
                
                // Perform the filtering in the database query instead of in memory
                var groups = await _context.ChatGroups
                    .Where(g => !g.IsArchived && !g.IsPrivate)
                    .Where(g => g.Name.Contains(searchTerm) || 
                           (g.Description != null && g.Description.Contains(searchTerm)))
                    .OrderByDescending(g => g.CreatedAt)
                    .Select(g => new ChatGroup
                    {
                        Id = g.Id,
                        Name = g.Name,
                        Description = g.Description,
                        IsPrivate = g.IsPrivate,
                        CreatedBy = g.CreatedBy,
                        CreatedAt = g.CreatedAt,
                        UpdatedAt = g.UpdatedAt,
                        ImageUrl = g.ImageUrl,
                        IsArchived = g.IsArchived,
                        IsDirectMessage = g.IsDirectMessage
                    })
                    .ToListAsync();

                _logger.LogInformation("Found {Count} groups matching search term: {SearchTerm}", 
                    groups.Count, searchTerm);
                return Ok(groups);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching groups with term {SearchTerm}", searchTerm);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/ChatGroups/public
        [HttpGet("public")]
        public async Task<ActionResult<IEnumerable<ChatGroup>>> GetPublicGroups()
        {
            try
            {
                _logger.LogInformation("Fetching public groups");
                
                var publicGroups = await _context.ChatGroups
                    .Where(g => !g.IsArchived && !g.IsPrivate && !g.IsDirectMessage)
                    .OrderByDescending(g => g.CreatedAt)
                    .Select(g => new ChatGroup
                    {
                        Id = g.Id,
                        Name = g.Name,
                        Description = g.Description,
                        IsPrivate = g.IsPrivate,
                        CreatedBy = g.CreatedBy,
                        CreatedAt = g.CreatedAt,
                        UpdatedAt = g.UpdatedAt,
                        ImageUrl = g.ImageUrl,
                        IsArchived = g.IsArchived,
                        IsDirectMessage = g.IsDirectMessage
                    })
                    .ToListAsync();

                _logger.LogInformation("Successfully retrieved {Count} public groups", publicGroups.Count);
                return Ok(publicGroups);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting public groups");
                return StatusCode(500, "Internal server error");
            }
        }

        // POST: api/ChatGroups/5/members
        [HttpPost("{groupId}/members")]
        public async Task<IActionResult> AddMemberToGroup(int groupId, [FromBody] AddMemberRequest request)
        {
            try
            {
                var group = await _context.ChatGroups.FindAsync(groupId);
                if (group == null)
                {
                    return NotFound("Group not found");
                }

                var user = await _context.Users.FindAsync(request.UserId);
                if (user == null)
                {
                    return NotFound("User not found");
                }

                // Check if user is already a member
                var existingMember = await _context.GroupMembers
                    .FirstOrDefaultAsync(m => m.GroupId == groupId && m.UserId == request.UserId);

                if (existingMember != null)
                {
                    return BadRequest("User is already a member of this group");
                }

                var member = new GroupMember
                {
                    GroupId = groupId,
                    UserId = request.UserId,
                    Role = 1, // 1 = member
                    JoinedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.GroupMembers.Add(member);
                await _context.SaveChangesAsync();

                // Notify group members via SignalR
                await _chatHubContext.Clients.Group(groupId.ToString())
                    .SendAsync("UserJoinedGroup", new
                    {
                        GroupId = groupId,
                        UserId = request.UserId,
                        UserName = user.Name,
                        JoinedAt = member.JoinedAt
                    });

                return Ok(new { message = "User added to group successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding member to group {GroupId}", groupId);
                return StatusCode(500, "Internal server error");
            }
        }

        // DELETE: api/ChatGroups/5/members/3
        [HttpDelete("{groupId}/members/{userId}")]
        public async Task<IActionResult> RemoveMemberFromGroup(int groupId, int userId)
        {
            try
            {
                var member = await _context.GroupMembers
                    .FirstOrDefaultAsync(m => m.GroupId == groupId && m.UserId == userId);

                if (member == null)
                {
                    return NotFound("Member not found in group");
                }

                _context.GroupMembers.Remove(member);
                await _context.SaveChangesAsync();

                // Notify group members via SignalR
                await _chatHubContext.Clients.Group(groupId.ToString())
                    .SendAsync("UserLeftGroup", new
                    {
                        GroupId = groupId,
                        UserId = userId,
                        LeftAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                    });

                return Ok(new { message = "User removed from group successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing member from group {GroupId}", groupId);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/ChatGroups/5/members
        [HttpGet("{groupId}/members")]
        public async Task<ActionResult<IEnumerable<object>>> GetGroupMembers(int groupId)
        {
            try
            {
                _logger.LogInformation("Fetching members for group {GroupId}", groupId);
                
                var group = await _context.ChatGroups.FindAsync(groupId);
                if (group == null)
                {
                    _logger.LogWarning("Group {GroupId} not found", groupId);
                    return NotFound("Group not found");
                }

                // Use a more efficient query with projection
                var members = await _context.GroupMembers
                    .Where(m => m.GroupId == groupId && !m.IsDeleted)
                    .Join(_context.Users.Where(u => !u.IsDeleted && u.IsActive),
                        m => m.UserId,
                        u => u.Id,
                        (m, u) => new
                        {
                            m.Id,
                            m.GroupId,
                            m.UserId,
                            m.Role,
                            m.JoinedAt,
                            m.IsDeleted,
                            m.LeftAt,
                            // ✅ إرسال كائن User كامل
                            User = new
                            {
                                u.Id,
                                u.Name,
                                u.Email,
                                ProfileImage = u.ProfileImage,
                                u.IsActive,
                                u.IsOnline,
                                u.CreatedAt
                            }
                        })
                    .OrderBy(m => m.User.Name)
                    .ToListAsync();

                _logger.LogInformation("Successfully retrieved {Count} members for group {GroupId}", 
                    members.Count, groupId);
                return Ok(members);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting members for group {GroupId}", groupId);
                return StatusCode(500, "Internal server error");
            }
        }

        // PUT: api/ChatGroups/5/members/3/role
        [HttpPut("{groupId}/members/{userId}/role")]
        public async Task<IActionResult> UpdateMemberRole(int groupId, int userId, [FromBody] UpdateRoleRequest request)
        {
            try
            {
                var member = await _context.GroupMembers
                    .FirstOrDefaultAsync(m => m.GroupId == groupId && m.UserId == userId);

                if (member == null)
                {
                    return NotFound("Member not found in group");
                }

                member.Role = request.Role;
                await _context.SaveChangesAsync();

                return Ok(new { message = "Member role updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating member role in group {GroupId}", groupId);
                return StatusCode(500, "Internal server error");
            }
        }

        // POST: api/ChatGroups/5/join
        [HttpPost("{groupId}/join")]
        public async Task<IActionResult> JoinGroup(int groupId, [FromBody] JoinGroupRequest request)
        {
            try
            {
                var group = await _context.ChatGroups.FindAsync(groupId);
                if (group == null)
                {
                    return NotFound("Group not found");
                }

                if (group.IsPrivate)
                {
                    return BadRequest("Cannot join private group without invitation");
                }

                var user = await _context.Users.FindAsync(request.UserId);
                if (user == null)
                {
                    return NotFound("User not found");
                }

                // Check if user is already a member
                var existingMember = await _context.GroupMembers
                    .FirstOrDefaultAsync(m => m.GroupId == groupId && m.UserId == request.UserId);

                if (existingMember != null)
                {
                    return BadRequest("User is already a member of this group");
                }

                var member = new GroupMember
                {
                    GroupId = groupId,
                    UserId = request.UserId,
                    Role = 1, // 1 = member
                    JoinedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.GroupMembers.Add(member);
                await _context.SaveChangesAsync();

                // Notify group members via SignalR
                await _chatHubContext.Clients.Group(groupId.ToString())
                    .SendAsync("UserJoinedGroup", new
                    {
                        GroupId = groupId,
                        UserId = request.UserId,
                        UserName = user.Name,
                        JoinedAt = member.JoinedAt
                    });

                return Ok(new { message = "Joined group successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error joining group {GroupId}", groupId);
                return StatusCode(500, "Internal server error");
            }
        }

        // DELETE: api/ChatGroups/5/leave/3
        [HttpDelete("{groupId}/leave/{userId}")]
        public async Task<IActionResult> LeaveGroup(int groupId, int userId)
        {
            try
            {
                var member = await _context.GroupMembers
                    .FirstOrDefaultAsync(m => m.GroupId == groupId && m.UserId == userId);

                if (member == null)
                {
                    return NotFound("User is not a member of this group");
                }

                _context.GroupMembers.Remove(member);
                await _context.SaveChangesAsync();

                // Notify group members via SignalR
                await _chatHubContext.Clients.Group(groupId.ToString())
                    .SendAsync("UserLeftGroup", new
                    {
                        GroupId = groupId,
                        UserId = userId,
                        LeftAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                    });

                return Ok(new { message = "Left group successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error leaving group {GroupId}", groupId);
                return StatusCode(500, "Internal server error");
            }
        }

        // PUT: api/ChatGroups/5/archive
        [HttpPut("{id}/archive")]
        public async Task<IActionResult> ArchiveGroup(int id)
        {
            try
            {
                var chatGroup = await _context.ChatGroups.FindAsync(id);
                if (chatGroup == null)
                {
                    return NotFound();
                }

                chatGroup.IsArchived = true;
                chatGroup.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();

                return Ok(new { message = "Group archived successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error archiving group {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // PUT: api/ChatGroups/5/unarchive
        [HttpPut("{id}/unarchive")]
        public async Task<IActionResult> UnarchiveGroup(int id)
        {
            try
            {
                var chatGroup = await _context.ChatGroups.FindAsync(id);
                if (chatGroup == null)
                {
                    return NotFound();
                }

                chatGroup.IsArchived = false;
                chatGroup.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();

                return Ok(new { message = "Group unarchived successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unarchiving group {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/ChatGroups/user/5/direct-messages
        [HttpGet("user/{userId}/direct-messages")]
        public async Task<ActionResult<IEnumerable<ChatGroup>>> GetUserDirectMessages(int userId)
        {
            try
            {
                _logger.LogInformation("Fetching direct messages for user {UserId}", userId);
                
                // Check if user exists
                var userExists = await _context.Users.AnyAsync(u => u.Id == userId);
                if (!userExists)
                {
                    _logger.LogWarning("User {UserId} not found", userId);
                    return NotFound("User not found");
                }
                
                // Use a more efficient query with joins
                var userGroups = await _context.GroupMembers
                    .Where(m => m.UserId == userId)
                    .Join(_context.ChatGroups.Where(g => !g.IsArchived && g.IsDirectMessage),
                        m => m.GroupId,
                        g => g.Id,
                        (m, g) => g)
                    .OrderByDescending(g => g.CreatedAt)
                    .Select(g => new ChatGroup
                    {
                        Id = g.Id,
                        Name = g.Name,
                        Description = g.Description,
                        IsPrivate = g.IsPrivate,
                        CreatedBy = g.CreatedBy,
                        CreatedAt = g.CreatedAt,
                        UpdatedAt = g.UpdatedAt,
                        ImageUrl = g.ImageUrl,
                        IsArchived = g.IsArchived,
                        IsDirectMessage = g.IsDirectMessage
                    })
                    .ToListAsync();

                _logger.LogInformation("Successfully retrieved {Count} direct messages for user {UserId}", 
                    userGroups.Count, userId);
                return Ok(userGroups);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting direct messages for user {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        private bool ChatGroupExists(int id)
        {
            return _context.ChatGroups.Any(e => e.Id == id);
        }

        // GET: api/ChatGroups/debug/membership
        [HttpGet("debug/membership")]
        public async Task<ActionResult> DebugMembership()
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                _logger.LogInformation("تشخيص عضوية المجموعات للمستخدم {UserId}", currentUserId);

                // 1. جلب جميع المجموعات
                var allGroups = await _context.ChatGroups
                    .Where(g => !g.IsDeleted && !g.IsArchived)
                    .Select(g => new { g.Id, g.Name, g.CreatedBy, g.IsPrivate })
                    .ToListAsync();

                // 2. جلب عضوية المستخدم
                var userMemberships = await _context.GroupMembers
                    .Where(m => m.UserId == currentUserId && !m.IsDeleted && m.LeftAt == null)
                    .Select(m => new { m.GroupId, m.Role, m.JoinedAt })
                    .ToListAsync();

                // 3. جلب إحصائيات كل مجموعة
                var groupStats = await _context.ChatGroups
                    .Where(g => !g.IsDeleted && !g.IsArchived)
                    .Select(g => new
                    {
                        GroupId = g.Id,
                        GroupName = g.Name,
                        CreatedBy = g.CreatedBy,
                        IsPrivate = g.IsPrivate,
                        MemberCount = _context.GroupMembers
                            .Count(m => m.GroupId == g.Id && !m.IsDeleted && m.LeftAt == null),
                        MessageCount = _context.Messages
                            .Count(m => m.GroupId == g.Id && !m.IsDeleted),
                        UserIsMember = _context.GroupMembers
                            .Any(m => m.GroupId == g.Id && m.UserId == currentUserId && !m.IsDeleted && m.LeftAt == null)
                    })
                    .ToListAsync();

                return Ok(new
                {
                    CurrentUserId = currentUserId,
                    AllGroupsCount = allGroups.Count,
                    UserMembershipsCount = userMemberships.Count,
                    AllGroups = allGroups,
                    UserMemberships = userMemberships,
                    GroupStatistics = groupStats
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تشخيص عضوية المجموعات");
                return StatusCode(500, "خطأ داخلي في الخادم");
            }
        }
    }

    public class AddMemberRequest
    {
        public int UserId { get; set; }
    }

    public class UpdateRoleRequest
    {
        public int Role { get; set; } = 1; // 1=member, 2=moderator, 3=admin
    }

    public class JoinGroupRequest
    {
        public int UserId { get; set; }
    }
}