import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../services/unified_permission_service.dart';
import '../controllers/auth_controller.dart';

/// مساعد تشخيص مشاكل لوحة التحكم الإدارية
class AdminDebugHelper {
  static final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  static final AuthController _authController = Get.find<AuthController>();

  /// تشخيص شامل لمشاكل لوحة التحكم الإدارية
  static Future<void> diagnoseAdminIssues() async {
    // التحقق من صلاحية الاختبار أولاً
    if (!_permissionService.canAccessTesting()) {
      debugPrint('❌ ليس لديك صلاحية الوصول لأدوات التشخيص (testing.access)');
      return;
    }

    debugPrint('🔍 ===== تشخيص مشاكل لوحة التحكم الإدارية =====');

    // 1. فحص المستخدم الحالي
    _checkCurrentUser();

    // 2. فحص الصلاحيات
    await _checkPermissions();

    // 3. فحص الاتصال بالخدمات
    _checkServices();

    debugPrint('🔍 ===== انتهاء التشخيص =====');
  }

  /// فحص المستخدم الحالي
  static void _checkCurrentUser() {
    debugPrint('\n📋 فحص المستخدم الحالي:');
    
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) {
      debugPrint('❌ لا يوجد مستخدم مسجل دخول');
      return;
    }
    
    debugPrint('✅ المستخدم: ${currentUser.name}');
    debugPrint('✅ الدور: ${currentUser.role.value}');
    debugPrint('✅ ID: ${currentUser.id}');
    debugPrint('✅ نشط: ${currentUser.isActive}');
  }

  /// فحص الصلاحيات
  static Future<void> _checkPermissions() async {
    debugPrint('\n🔐 فحص الصلاحيات:');
    
    // تحديث الصلاحيات أولاً
    try {
      await _permissionService.refreshCurrentUserPermissions();
      debugPrint('✅ تم تحديث الصلاحيات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث الصلاحيات: $e');
    }
    
    // فحص الصلاحيات الأساسية
    final permissions = {
      'admin.view': _permissionService.canAccessAdmin(),
      'users.view': _permissionService.canViewAllUsers(),
      'permissions.manage': _permissionService.canManagePermissions(),
      'system.backup': _permissionService.canBackupSystem(),
      'dashboard.admin': _permissionService.canAccessDashboard(),
    };
    
    debugPrint('📊 الصلاحيات الأساسية:');
    permissions.forEach((permission, hasIt) {
      final status = hasIt ? '✅' : '❌';
      debugPrint('  $status $permission: $hasIt');
    });
    
    debugPrint('📊 إجمالي صلاحيات المستخدم: ${_permissionService.userPermissions.length}');
    
    // طباعة أول 15 صلاحية للمراجعة
    if (_permissionService.userPermissions.isNotEmpty) {
      debugPrint('📋 عينة من الصلاحيات:');
      final sample = _permissionService.userPermissions.entries.take(15);
      for (final permission in sample) {
        final status = permission.value ? '✅' : '❌';
        debugPrint('  $status ${permission.key}: ${permission.value}');
      }
    } else {
      debugPrint('❌ لا توجد صلاحيات محملة للمستخدم');
    }
  }

  /// فحص الخدمات
  static void _checkServices() {
    debugPrint('\n🔧 فحص الخدمات:');
    
    try {
      Get.find<UnifiedPermissionService>();
      debugPrint('✅ UnifiedPermissionService متاح');
    } catch (e) {
      debugPrint('❌ خطأ في UnifiedPermissionService: $e');
    }

    try {
      Get.find<AuthController>();
      debugPrint('✅ AuthController متاح');
    } catch (e) {
      debugPrint('❌ خطأ في AuthController: $e');
    }
  }

  /// فحص صلاحية محددة
  static void checkSpecificPermission(String permissionName) {
    // التحقق من صلاحية الاختبار أولاً
    if (!_permissionService.canAccessTesting()) {
      debugPrint('❌ ليس لديك صلاحية الوصول لأدوات التشخيص (testing.access)');
      return;
    }

    debugPrint('\n🔍 فحص صلاحية محددة: $permissionName');

    final hasPermission = _permissionService.hasPermission(permissionName);
    final status = hasPermission ? '✅' : '❌';

    debugPrint('$status النتيجة: $hasPermission');
    
    // البحث في جميع الصلاحيات المحملة
    final allPermissions = _permissionService.userPermissions;
    final matchingKeys = allPermissions.keys.where((key) => 
      key.toLowerCase().contains(permissionName.toLowerCase())
    ).toList();
    
    if (matchingKeys.isNotEmpty) {
      debugPrint('🔍 صلاحيات مشابهة موجودة:');
      for (final key in matchingKeys) {
        debugPrint('  - $key: ${allPermissions[key]}');
      }
    } else {
      debugPrint('❌ لا توجد صلاحيات مشابهة');
    }
  }

  /// طباعة جميع الصلاحيات
  static void printAllPermissions() {
    // التحقق من صلاحية الاختبار أولاً
    if (!_permissionService.canAccessTesting()) {
      debugPrint('❌ ليس لديك صلاحية الوصول لأدوات التشخيص (testing.access)');
      return;
    }

    debugPrint('\n📋 جميع صلاحيات المستخدم:');

    final permissions = _permissionService.userPermissions;
    if (permissions.isEmpty) {
      debugPrint('❌ لا توجد صلاحيات محملة');
      return;
    }
    
    final sortedPermissions = permissions.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));
    
    for (final permission in sortedPermissions) {
      final status = permission.value ? '✅' : '❌';
      debugPrint('  $status ${permission.key}: ${permission.value}');
    }
    
    debugPrint('\n📊 الإحصائيات:');
    final activePermissions = permissions.values.where((v) => v).length;
    final totalPermissions = permissions.length;
    debugPrint('  - الصلاحيات النشطة: $activePermissions');
    debugPrint('  - إجمالي الصلاحيات: $totalPermissions');
    debugPrint('  - النسبة: ${(activePermissions / totalPermissions * 100).toStringAsFixed(1)}%');
  }

  /// تشخيص سريع لنظام مرفقات الرسائل
  static Future<void> diagnoseMessageAttachments() async {
    if (!_permissionService.canAccessTesting()) {
      debugPrint('❌ ليس لديك صلاحية الوصول لأدوات التشخيص');
      return;
    }

    debugPrint('🔍 ===== تشخيص نظام مرفقات الرسائل =====');
    await MessageAttachmentsDiagnostic.runFullDiagnostic();
    debugPrint('🔍 ===== انتهاء تشخيص المرفقات =====');
  }
}
