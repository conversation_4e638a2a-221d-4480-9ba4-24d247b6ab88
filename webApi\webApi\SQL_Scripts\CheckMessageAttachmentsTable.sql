-- فحص بنية جدول message_attachments وإصلاح المشاكل
USE [TaskManagementDB]
GO

PRINT '🔍 === فحص بنية جدول message_attachments ==='

-- 1. فحص وجود الجدول
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'message_attachments')
BEGIN
    PRINT '✅ جدول message_attachments موجود'
    
    -- عرض بنية الجدول
    PRINT '📋 بنية الجدول:'
    SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT,
        CHARACTER_MAXIMUM_LENGTH
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'message_attachments'
    ORDER BY ORDINAL_POSITION
END
ELSE
BEGIN
    PRINT '❌ جدول message_attachments غير موجود - سيتم إنشاؤه'
    
    -- إنشاء الجدول
    CREATE TABLE [dbo].[message_attachments](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [message_id] [int] NOT NULL,
        [file_name] [nvarchar](255) NOT NULL,
        [file_path] [nvarchar](255) NOT NULL,
        [file_size] [bigint] NOT NULL,
        [file_type] [nvarchar](50) NOT NULL,
        [uploaded_at] [bigint] NOT NULL,
        [uploaded_by] [int] NULL,
        [is_deleted] [bit] NOT NULL DEFAULT 0,
        CONSTRAINT [PK_message_attachments] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_message_attachments_messages] FOREIGN KEY([message_id]) REFERENCES [dbo].[messages] ([id]),
        CONSTRAINT [FK_message_attachments_users] FOREIGN KEY([uploaded_by]) REFERENCES [dbo].[users] ([id])
    )
    
    PRINT '✅ تم إنشاء جدول message_attachments'
END

-- 2. فحص وجود العمود uploaded_by
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'message_attachments' AND COLUMN_NAME = 'uploaded_by')
BEGIN
    PRINT '✅ العمود uploaded_by موجود'
END
ELSE
BEGIN
    PRINT '❌ العمود uploaded_by غير موجود'
    
    -- فحص إذا كان هناك عمود بأسم مختلف
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'message_attachments' AND COLUMN_NAME = 'user_id')
    BEGIN
        PRINT '🔄 وجد عمود user_id - سيتم إعادة تسميته إلى uploaded_by'
        EXEC sp_rename 'message_attachments.user_id', 'uploaded_by', 'COLUMN'
        PRINT '✅ تم إعادة تسمية العمود'
    END
    ELSE IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'message_attachments' AND COLUMN_NAME = 'UserId')
    BEGIN
        PRINT '🔄 وجد عمود UserId - سيتم إعادة تسميته إلى uploaded_by'
        EXEC sp_rename 'message_attachments.UserId', 'uploaded_by', 'COLUMN'
        PRINT '✅ تم إعادة تسمية العمود'
    END
    ELSE
    BEGIN
        PRINT '🔄 إضافة العمود uploaded_by'
        ALTER TABLE message_attachments ADD uploaded_by INT NULL
        PRINT '✅ تم إضافة العمود uploaded_by'
    END
END

-- 3. فحص وجود جدول users
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'users')
BEGIN
    PRINT '✅ جدول users موجود'
    
    -- فحص العمود id في جدول users
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'id')
    BEGIN
        PRINT '✅ العمود id موجود في جدول users'
    END
    ELSE
    BEGIN
        PRINT '❌ العمود id غير موجود في جدول users'
        
        -- فحص أسماء أعمدة أخرى
        PRINT '📋 أعمدة جدول users:'
        SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users'
    END
END
ELSE
BEGIN
    PRINT '❌ جدول users غير موجود'
END

-- 4. فحص Foreign Key
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME = 'FK_message_attachments_users')
BEGIN
    PRINT '✅ Foreign Key FK_message_attachments_users موجود'
END
ELSE
BEGIN
    PRINT '🔄 إضافة Foreign Key'
    
    -- التأكد من وجود الجداول والأعمدة أولاً
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'users') 
       AND EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'id')
       AND EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'message_attachments' AND COLUMN_NAME = 'uploaded_by')
    BEGIN
        ALTER TABLE message_attachments 
        ADD CONSTRAINT FK_message_attachments_users 
        FOREIGN KEY (uploaded_by) REFERENCES users(id)
        
        PRINT '✅ تم إضافة Foreign Key'
    END
    ELSE
    BEGIN
        PRINT '⚠️ لا يمكن إضافة Foreign Key - تحقق من وجود الجداول والأعمدة'
    END
END

-- 5. اختبار إدراج بيانات تجريبية
PRINT '🧪 اختبار إدراج بيانات تجريبية...'

-- التحقق من وجود رسالة للاختبار
DECLARE @TestMessageId INT = 1
DECLARE @TestUserId INT = 1

IF EXISTS (SELECT 1 FROM messages WHERE id = @TestMessageId)
   AND EXISTS (SELECT 1 FROM users WHERE id = @TestUserId)
BEGIN
    -- محاولة إدراج مرفق تجريبي
    BEGIN TRY
        INSERT INTO message_attachments (message_id, file_name, file_path, file_size, file_type, uploaded_at, uploaded_by, is_deleted)
        VALUES (@TestMessageId, 'test_file.txt', '/uploads/test_file.txt', 1024, 'text/plain', DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()), @TestUserId, 0)
        
        DECLARE @TestAttachmentId INT = SCOPE_IDENTITY()
        PRINT '✅ تم إدراج مرفق تجريبي بنجاح - معرف: ' + CAST(@TestAttachmentId AS NVARCHAR(10))
        
        -- حذف المرفق التجريبي
        DELETE FROM message_attachments WHERE id = @TestAttachmentId
        PRINT '🧹 تم حذف المرفق التجريبي'
        
    END TRY
    BEGIN CATCH
        PRINT '❌ فشل في إدراج المرفق التجريبي: ' + ERROR_MESSAGE()
    END CATCH
END
ELSE
BEGIN
    PRINT '⚠️ لا توجد رسالة أو مستخدم للاختبار (message_id=1, user_id=1)'
END

-- 6. عرض إحصائيات الجدول
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'message_attachments')
BEGIN
    DECLARE @AttachmentCount INT
    SELECT @AttachmentCount = COUNT(*) FROM message_attachments WHERE is_deleted = 0
    
    PRINT '📊 إحصائيات الجدول:'
    PRINT '   📎 عدد المرفقات النشطة: ' + CAST(@AttachmentCount AS NVARCHAR(10))
    
    IF @AttachmentCount > 0
    BEGIN
        PRINT '📋 عينة من المرفقات:'
        SELECT TOP 5 
            id,
            message_id,
            file_name,
            file_size,
            uploaded_by,
            is_deleted
        FROM message_attachments 
        ORDER BY uploaded_at DESC
    END
END

PRINT '✅ === انتهى فحص جدول message_attachments ==='
