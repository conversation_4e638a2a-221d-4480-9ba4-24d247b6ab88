import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/message_models.dart';
import '../../../models/user_model.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/date_time_formatter.dart';
import '../../../controllers/user_controller.dart';
import '../../../controllers/unified_chat_controller.dart';
import 'message_reactions_widget.dart';

/// ويدجت لعرض عنصر رسالة في المحادثة
class MessageItemWidget extends StatefulWidget {
  /// الرسالة
  final Message message;

  /// ما إذا كان المستخدم الحالي هو مرسل الرسالة
  final bool isCurrentUser;

  /// ما إذا كان يجب عرض الصورة الرمزية
  final bool showAvatar;

  /// معرف المستخدم الحالي
  final String currentUserId;

  /// ما إذا كان المستخدم الحالي مشرفًا
  final bool isAdmin;

  /// دالة يتم استدعاؤها عند الرد على الرسالة
  final Function(Message)? onReply;

  /// دالة يتم استدعاؤها عند تثبيت الرسالة
  final Function(Message)? onPin;

  /// دالة يتم استدعاؤها عند إلغاء تثبيت الرسالة
  final Function(Message)? onUnpin;

  /// دالة يتم استدعاؤها عند تعليم الرسالة للمتابعة
  final Function(Message)? onMarkForFollowUp;

  /// دالة يتم استدعاؤها عند تحرير الرسالة
  final Function(Message)? onEdit;

  /// دالة يتم استدعاؤها عند حذف الرسالة
  final Function(Message)? onDelete;

  const MessageItemWidget({
    super.key,
    required this.message,
    required this.isCurrentUser,
    required this.showAvatar,
    required this.currentUserId,
    this.isAdmin = false,
    this.onReply,
    this.onPin,
    this.onUnpin,
    this.onMarkForFollowUp,
    this.onEdit,
    this.onDelete,
  });

  @override
  State<MessageItemWidget> createState() => _MessageItemWidgetState();
}

class _MessageItemWidgetState extends State<MessageItemWidget> {
  final UserController _userController = Get.find<UserController>();

  User? _sender;
  User? _replyToUser;
  Message? _replyToMessage;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void didUpdateWidget(MessageItemWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.message.id != widget.message.id) {
      _loadData();
    }
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل معلومات المرسل
      _sender = _userController.getUserById(widget.message.senderId);

      // تحميل معلومات الرسالة المرد عليها إذا وجدت
      if (widget.message.replyToMessageId != null) {
        try {
          // محاولة الحصول على الرسالة المرد عليها من المتحكم الموحد
          final chatController = Get.find<UnifiedChatController>();
          // البحث في قائمة الرسائل المحملة
          _replyToMessage = chatController.messages.firstWhereOrNull(
            (msg) => msg.id == widget.message.replyToMessageId,
          );

          if (_replyToMessage != null) {
            _replyToUser = _userController.getUserById(_replyToMessage!.senderId);
          }
        } catch (e) {
          debugPrint('خطأ في تحميل الرسالة المرد عليها: $e');
          // في حالة عدم وجود المتحكم، نحاول إنشاء رسالة مؤقتة
          _replyToMessage = null;
          _replyToUser = null;
        }
      }
    } catch (e) {
      // التعامل مع الخطأ بصمت
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// عرض قائمة خيارات الرسالة
  void _showMessageOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.reply),
            title: const Text('رد'),
            onTap: () {
              Navigator.pop(context);
              if (widget.onReply != null) {
                widget.onReply!(widget.message);
              }
            },
          ),
          if (widget.isAdmin || widget.isCurrentUser)
            ListTile(
              leading: Icon(widget.message.isPinned
                  ? Icons.push_pin_outlined
                  : Icons.push_pin),
              title: Text(widget.message.isPinned ? 'إلغاء التثبيت' : 'تثبيت'),
              onTap: () {
                Navigator.pop(context);
                if (widget.message.isPinned) {
                  if (widget.onUnpin != null) {
                    widget.onUnpin!(widget.message);
                  } else {
                    // تنفيذ إلغاء التثبيت
                    debugPrint('إلغاء تثبيت الرسالة: ${widget.message.id}');
                  }
                } else {
                  if (widget.onPin != null) {
                    widget.onPin!(widget.message);
                  } else {
                    // تنفيذ التثبيت
                    debugPrint('تثبيت الرسالة: ${widget.message.id}');
                  }
                }
              },
            ),
          ListTile(
            leading: const Icon(Icons.flag),
            title: const Text('تعليم للمتابعة'),
            onTap: () {
              Navigator.pop(context);
              if (widget.onMarkForFollowUp != null) {
                widget.onMarkForFollowUp!(widget.message);
              }
            },
          ),
          if (widget.isCurrentUser)
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('تحرير'),
              onTap: () {
                Navigator.pop(context);
                if (widget.onEdit != null) {
                  widget.onEdit!(widget.message);
                }
              },
            ),
          if (widget.isCurrentUser || widget.isAdmin)
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('حذف', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                if (widget.onDelete != null) {
                  widget.onDelete!(widget.message);
                }
              },
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const SizedBox(
        height: 50,
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final senderName = _sender?.name ?? 'مستخدم غير معروف';

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: widget.isCurrentUser
            ? MainAxisAlignment.end
            : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!widget.isCurrentUser && widget.showAvatar)
            CircleAvatar(
              radius: 16,
              backgroundImage: _sender?.profileImage != null
                  ? NetworkImage(_sender!.profileImage!)
                  : null,
              child: _sender?.profileImage == null
                  ? Text(senderName.isNotEmpty ? senderName[0] : '?')
                  : null,
            ),
          if (!widget.isCurrentUser && !widget.showAvatar)
            const SizedBox(width: 32),
          const SizedBox(width: 8),
          Flexible(
            child: Column(
              crossAxisAlignment: widget.isCurrentUser
                  ? CrossAxisAlignment.end
                  : CrossAxisAlignment.start,
              children: [
                // اسم المرسل (يظهر فقط إذا لم يكن المستخدم الحالي وكان يجب عرض الصورة الرمزية)
                if (!widget.isCurrentUser && widget.showAvatar)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4.0),
                    child: Text(
                      senderName,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),

                // الرسالة المرد عليها
                if (widget.message.replyToMessageId != null &&
                    _replyToMessage != null)
                  Container(
                    margin: const EdgeInsets.only(bottom: 4.0),
                    padding: const EdgeInsets.all(8.0),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(8.0),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'رد على ${_replyToUser?.name ?? 'مستخدم غير معروف'}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 10,
                            color: Colors.grey,
                          ),
                        ),
                        Text(
                          _replyToMessage!.content,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.black87,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),

                // فقاعة الرسالة
                GestureDetector(
                  onLongPress: _showMessageOptions,
                  child: Container(
                    padding: const EdgeInsets.all(12.0),
                    decoration: BoxDecoration(
                      color: widget.isCurrentUser
                          ? AppColors.primary
                          : Colors.grey[200],
                      borderRadius: BorderRadius.circular(16.0),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // محتوى الرسالة
                        Text(
                          widget.message.content,
                          style: TextStyle(
                            color: widget.isCurrentUser
                                ? Colors.white
                                : Colors.black,
                          ),
                        ),

                        const SizedBox(height: 4),

                        // الوقت وحالة القراءة
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              DateTimeFormatter.formatTime(
                                  DateTime.fromMillisecondsSinceEpoch(widget.message.createdAt * 1000)),
                              style: TextStyle(
                                fontSize: 10,
                                color: widget.isCurrentUser
                                    ? Colors.white70
                                    : Colors.black54,
                              ),
                            ),
                            if (widget.isCurrentUser)
                              Padding(
                                padding: const EdgeInsets.only(right: 4),
                                child: Icon(
                                  widget.message.isRead
                                      ? Icons.done_all
                                      : Icons.done,
                                  size: 12,
                                  color: widget.isCurrentUser
                                      ? Colors.white70
                                      : Colors.black54,
                                ),
                              ),
                            if (widget.message.isPinned)
                              Padding(
                                padding: const EdgeInsets.only(right: 4),
                                child: Icon(
                                  Icons.push_pin,
                                  size: 12,
                                  color: widget.isCurrentUser
                                      ? Colors.white70
                                      : Colors.black54,
                                ),
                              ),
                            if (widget.message.priority !=
                                MessagePriority.normal)
                              Padding(
                                padding: const EdgeInsets.only(right: 4),
                                child: Icon(
                                  Icons.priority_high,
                                  size: 12,
                                  color: widget.isCurrentUser
                                      ? Colors.white70
                                      : Colors.black54,
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                // تفاعلات الرسالة
                MessageReactionsWidget(
                  messageId: widget.message.id.toString(),
                  currentUserId: widget.currentUserId,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
