import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import '../../../services/unified_permission_service.dart';

/// ورقة خيارات المرفقات للمحادثات
class AttachmentOptionsSheet extends StatelessWidget {
  /// دالة يتم استدعاؤها عند اختيار صورة
  final Function(File file, String fileName)? onImageSelected;
  
  /// دالة يتم استدعاؤها عند اختيار ملف
  final Function(File file, String fileName)? onFileSelected;
  
  /// دالة يتم استدعاؤها عند التقاط صورة من الكاميرا
  final Function(File file, String fileName)? onCameraSelected;

  const AttachmentOptionsSheet({
    super.key,
    this.onImageSelected,
    this.onFileSelected,
    this.onCameraSelected,
  });

  @override
  Widget build(BuildContext context) {
    final permissionService = Get.find<UnifiedPermissionService>();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مقبض السحب
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(bottom: 20),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // العنوان
          Text(
            'إرفاق ملف',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),

          // خيارات المرفقات
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // اختيار من المعرض
              if (permissionService.canUploadAttachments())
                _buildOptionButton(
                  context: context,
                  icon: Icons.photo_library,
                  label: 'المعرض',
                  color: Colors.purple,
                  onTap: _pickImageFromGallery,
                ),

              // التقاط صورة
              if (permissionService.canUploadAttachments())
                _buildOptionButton(
                  context: context,
                  icon: Icons.camera_alt,
                  label: 'الكاميرا',
                  color: Colors.blue,
                  onTap: _pickImageFromCamera,
                ),

              // اختيار ملف
              if (permissionService.canUploadAttachments())
                _buildOptionButton(
                  context: context,
                  icon: Icons.insert_drive_file,
                  label: 'ملف',
                  color: Colors.orange,
                  onTap: _pickFile,
                ),
            ],
          ),

          const SizedBox(height: 20),

          // زر الإلغاء
          SizedBox(
            width: double.infinity,
            child: TextButton(
              onPressed: () => Get.back(),
              child: Text(
                'إلغاء',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر خيار
  Widget _buildOptionButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(color: color.withOpacity(0.3)),
            ),
            child: Icon(
              icon,
              color: color,
              size: 30,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[700],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// اختيار صورة من المعرض
  Future<void> _pickImageFromGallery() async {
    try {
      Get.back(); // إغلاق الورقة أولاً

      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null) {
        final file = File(image.path);
        final fileName = image.name.isNotEmpty 
            ? image.name 
            : 'image_${DateTime.now().millisecondsSinceEpoch}.jpg';
        
        onImageSelected?.call(file, fileName);
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء اختيار الصورة: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _pickImageFromCamera() async {
    try {
      Get.back(); // إغلاق الورقة أولاً

      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
      );

      if (image != null) {
        final file = File(image.path);
        final fileName = 'camera_${DateTime.now().millisecondsSinceEpoch}.jpg';
        
        onCameraSelected?.call(file, fileName);
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء التقاط الصورة: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// اختيار ملف
  Future<void> _pickFile() async {
    try {
      Get.back(); // إغلاق الورقة أولاً

      final result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final platformFile = result.files.first;
        
        if (platformFile.path != null) {
          final file = File(platformFile.path!);
          final fileName = platformFile.name;
          
          onFileSelected?.call(file, fileName);
        } else {
          Get.snackbar(
            'تنبيه',
            'لا يمكن الوصول إلى مسار الملف',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.amber.shade100,
            colorText: Colors.amber.shade800,
          );
        }
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء اختيار الملف: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }
}
