import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/chat_group_models.dart';
import 'package:flutter_application_2/models/message_models.dart';
import 'package:get/get.dart';
import 'package:emoji_picker_flutter/emoji_picker_flutter.dart';
import '../../constants/app_colors.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/unified_chat_controller.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/message_reactions_controller.dart';
import '../../services/unified_signalr_service.dart';
import '../../services/unified_permission_service.dart';

import '../../models/user_model.dart';
import '../widgets/chat/message_item_widget.dart';
import '../widgets/chat/attachment_options_sheet.dart';
import '../../controllers/message_attachments_controller.dart';
import '../../utils/logger.dart';
import 'chat_info_screen.dart';
import 'add_members_screen.dart';
import 'search_conversation_screen.dart';
import 'mute_notifications_screen.dart';

/// شاشة تفاصيل المحادثة الموحدة
class UnifiedChatDetailScreen extends StatefulWidget {
  final ChatGroup chatGroup;

  const UnifiedChatDetailScreen({
    super.key,
    required this.chatGroup,
  });

  @override
  State<UnifiedChatDetailScreen> createState() =>
      _UnifiedChatDetailScreenState();
}

class _UnifiedChatDetailScreenState extends State<UnifiedChatDetailScreen> {
  final _chatController = Get.find<UnifiedChatController>();
  final _authController = Get.find<AuthController>();
  final _userController = Get.find<UserController>();
  final _permissionService = Get.find<UnifiedPermissionService>();
  final _attachmentsController = Get.put(MessageAttachmentsController());
  final _textController = TextEditingController();
  final _scrollController = ScrollController();

  // متغيرات للإشارة إلى المستخدمين
  final RxBool _showMentionsList = false.obs;
  final RxList<User> _filteredUsers = <User>[].obs;
  final RxList<int> _mentionedUserIds = <int>[].obs;

  // متغير للرد على رسالة
  final Rx<Message?> _replyToMessage = Rx<Message?>(null);

  // متغيرات لدعم الرموز التعبيرية
  final RxBool _showEmojiPicker = false.obs;
  final FocusNode _textFieldFocusNode = FocusNode();

  // متغير لتخزين مراقب التحديثات
  Worker? _messagesUpdateSubscription;

  // معرف الرسالة المراد التمرير إليها (إذا تم تمريره من شاشة البحث)
  String? _messageToScrollTo;

  // متغير لتتبع ما إذا كان التمرير قيد التنفيذ حاليًا
  bool _isScrolling = false;

  @override
  void initState() {
    super.initState();

    // تسجيل MessageReactionsController إذا لم يكن مسجلاً
    if (!Get.isRegistered<MessageReactionsController>()) {
      Get.put(MessageReactionsController());
    }

    // تهيئة المتغيرات
    _isScrolledUp.value = false;

    // إضافة مستمع للتمرير للصفحات
    _scrollController.addListener(_scrollListener);

    // تمرير ScrollController إلى المتحكم للتمرير التلقائي
    _chatController.setScrollController(_scrollController);

    // إضافة مستمع لحقل النص للكشف عن الإشارات
    _textController.addListener(_handleTextChange);

    // ملاحظة: تم إزالة الاشتراك في إشعارات التحديث لأنها غير متوفرة في UnifiedChatController الحالي
    // يمكن إضافة هذه الوظيفة لاحقاً إذا لزم الأمر

    // جدولة تحميل الرسائل ليتم بعد اكتمال إطار البناء الأول
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadMessages();
      _joinChatGroup();
    });
  }

  // متغير لتتبع حالة الكتابة
  bool _isTyping = false;
  // مؤقت لإرسال إشعارات الكتابة
  Timer? _typingTimer;

  /// معالجة تغيير النص للكشف عن الإشارات وإرسال إشعارات الكتابة
  void _handleTextChange() {
    final text = _textController.text;
    final cursorPosition = _textController.selection.baseOffset;

    // إرسال إشعار الكتابة
    _handleTypingNotification(text);

    // التحقق من وجود علامة @ قبل موضع المؤشر
    if (cursorPosition > 0 && text.isNotEmpty) {
      // البحث عن آخر علامة @ قبل موضع المؤشر
      int lastAtSymbol = text.substring(0, cursorPosition).lastIndexOf('@');

      if (lastAtSymbol != -1) {
        // التحقق من أن علامة @ ليست جزءًا من كلمة (يجب أن تكون في بداية الكلمة)
        bool isValidMention = lastAtSymbol == 0 ||
            text[lastAtSymbol - 1] == ' ' ||
            text[lastAtSymbol - 1] == '\n';

        if (isValidMention) {
          // استخراج النص بعد علامة @ وقبل موضع المؤشر
          String query =
              text.substring(lastAtSymbol + 1, cursorPosition).toLowerCase();

          // البحث عن المستخدمين المطابقين
          _filterUsers(query);

          // عرض قائمة الإشارات
          _showMentionsList.value = true;
          return;
        }
      }
    }

    // إخفاء قائمة الإشارات إذا لم يتم العثور على علامة @ صالحة
    _showMentionsList.value = false;
  }

  /// معالجة إشعارات الكتابة
  void _handleTypingNotification(String text) {
    // إذا كان النص فارغًا، نرسل إشعار توقف الكتابة
    if (text.isEmpty) {
      if (_isTyping) {
        _sendStoppedTypingNotification();
      }
      return;
    }

    // إذا لم يكن المستخدم يكتب بالفعل، نرسل إشعار بدء الكتابة
    if (!_isTyping) {
      _sendTypingNotification();
    }

    // إعادة ضبط المؤقت في كل مرة يتغير فيها النص
    _typingTimer?.cancel();
    _typingTimer = Timer(const Duration(seconds: 3), () {
      // بعد 3 ثوانٍ من عدم الكتابة، نرسل إشعار توقف الكتابة
      _sendStoppedTypingNotification();
    });
  }

  /// إرسال إشعار بدء الكتابة
  void _sendTypingNotification() {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null || widget.chatGroup.id == 0) return;

    _isTyping = true;
    // استخدام الدالة المخصصة للمجموعات العامة
    Get.find<UnifiedSignalRService>().sendGroupTypingIndicator(
      widget.chatGroup.id.toString(),
      currentUser.name,
    );
    AppLogger.debug('تم إرسال إشعار بدء الكتابة');
  }

  /// إرسال إشعار توقف الكتابة
  void _sendStoppedTypingNotification() {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null || widget.chatGroup.id == 0) return;

    _isTyping = false;
    // استخدام الدالة المخصصة للمجموعات العامة
    Get.find<UnifiedSignalRService>().sendGroupStoppedTypingIndicator(
      widget.chatGroup.id.toString(),
      currentUser.name,
    );
    AppLogger.debug('تم إرسال إشعار توقف الكتابة');
  }

  /// الانضمام لمجموعة المحادثة عبر SignalR
  void _joinChatGroup() {
    if (widget.chatGroup.id == 0) return;

    Get.find<UnifiedSignalRService>().joinChatGroup(
      widget.chatGroup.id.toString(),
    );
    AppLogger.debug('تم الانضمام لمجموعة المحادثة: ${widget.chatGroup.id}');
  }

  /// مغادرة مجموعة المحادثة عبر SignalR
  void _leaveChatGroup() {
    if (widget.chatGroup.id == 0) return;

    Get.find<UnifiedSignalRService>().leaveChatGroup(
      widget.chatGroup.id.toString(),
    );
    AppLogger.debug('تم مغادرة مجموعة المحادثة: ${widget.chatGroup.id}');
  }

  /// تصفية المستخدمين بناءً على الاستعلام
  void _filterUsers(String query) {
    if (query.isEmpty) {
      // إذا كان الاستعلام فارغًا، عرض جميع المستخدمين
      _filteredUsers.value = _userController.users.toList();
    } else {
      // تصفية المستخدمين بناءً على الاستعلام
      _filteredUsers.value = _userController.users
          .where((user) =>
              user.name.toLowerCase().contains(query) ||
              (user.email?.toLowerCase().contains(query) ?? false))
          .toList();
    }
  }

  /// إدراج إشارة إلى مستخدم في حقل النص
  void _insertMention(User user) {
    final text = _textController.text;
    final cursorPosition = _textController.selection.baseOffset;

    // البحث عن آخر علامة @ قبل موضع المؤشر
    int lastAtSymbol = text.substring(0, cursorPosition).lastIndexOf('@');

    if (lastAtSymbol != -1) {
      // استبدال النص من علامة @ إلى موضع المؤشر باسم المستخدم
      final newText =
          '${text.substring(0, lastAtSymbol)}@${user.name} ${text.substring(cursorPosition)}';

      // تحديث النص وموضع المؤشر
      _textController.value = TextEditingValue(
        text: newText,
        selection: TextSelection.collapsed(
          offset: lastAtSymbol + user.name.length + 2, // +2 for @ and space
        ),
      );

      // إضافة معرف المستخدم إلى قائمة المستخدمين المشار إليهم
      if (!_mentionedUserIds.contains(user.id)) {
        _mentionedUserIds.add(user.id);
      }
    }

    // إخفاء قائمة الإشارات
    _showMentionsList.value = false;
  }

  // متغير لتتبع ما إذا كان المستخدم بعيدًا عن نهاية المحادثة
  final RxBool _isScrolledUp = false.obs;

  // مقدار المسافة التي يجب أن يكون المستخدم بعيدًا عنها لاعتباره بعيدًا عن النهاية
  static const double _scrollThreshold = 100.0;

  void _scrollListener() {
    // تحميل المزيد من الرسائل عند الوصول إلى بداية القائمة
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      _loadMoreMessages();
    }

    // تحديث حالة التمرير لإظهار أو إخفاء زر التمرير إلى الأسفل
    if (_scrollController.hasClients) {
      final maxScroll = _scrollController.position.maxScrollExtent;
      final currentScroll = _scrollController.position.pixels;
      final isScrolledUp = maxScroll - currentScroll > _scrollThreshold;

      if (isScrolledUp != _isScrolledUp.value) {
        _isScrolledUp.value = isScrolledUp;
      }
    }
  }

  Future<void> _loadMessages() async {
    final currentUser = _authController.currentUser.value;
    if (currentUser != null) {
      try {
        // اختيار المجموعة وتحميل الرسائل
        await _chatController.setCurrentChatGroup(widget.chatGroup);

        // تأكد من أن القائمة تم تحديثها
        setState(() {});

        // التمرير إلى الأسفل بعد تحميل الرسائل
        // استخدام تأخير أطول للتأكد من أن القائمة تم بناؤها بالكامل
        Future.delayed(const Duration(milliseconds: 300), () {
          if (_messageToScrollTo != null) {
            _scrollToMessage(_messageToScrollTo!);
            _messageToScrollTo = null; // إعادة تعيين بعد التمرير
          } else {
            // استخدام jumpTo بدلاً من animateTo للتمرير الفوري عند فتح المحادثة
            if (_scrollController.hasClients) {
              try {
                _scrollController
                    .jumpTo(_scrollController.position.maxScrollExtent);
                AppLogger.debug('تم التمرير إلى آخر رسالة عند فتح المحادثة');
              } catch (e) {
                AppLogger.error('خطأ في التمرير إلى آخر رسالة', e);
                // محاولة مرة أخرى بعد تأخير إضافي
                Future.delayed(const Duration(milliseconds: 500), () {
                  _scrollToBottom();
                });
              }
            }
          }
          
          // تحديد جميع الرسائل كمقروءة بعد التحميل
          _markAllMessagesAsRead();
        });
      } catch (e) {
        // عرض رسالة خطأ
        Get.snackbar(
          'خطأ في تحميل الرسائل',
          'حدث خطأ أثناء تحميل الرسائل. يرجى المحاولة مرة أخرى.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
          duration: const Duration(seconds: 3),
        );
      }
    }
  }
  
  /// تحديد جميع رسائل المجموعة كمقروءة
  Future<void> _markAllMessagesAsRead() async {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null || widget.chatGroup.id == 0) return;

    // التحقق من وجود رسائل في المجموعة
    if (_chatController.messages.isEmpty) {
      AppLogger.debug('لا توجد رسائل في المجموعة لتحديدها كمقروءة');
      return;
    }

    try {
      // استدعاء API لتحديد جميع الرسائل كمقروءة
      await _chatController.markGroupMessagesAsRead(widget.chatGroup.id);
      AppLogger.debug('تم تحديد جميع رسائل المجموعة كمقروءة');
    } catch (e) {
      AppLogger.error('خطأ في تحديد الرسائل كمقروءة', e);
    }
  }

  /// التمرير إلى رسالة محددة بواسطة معرفها
  void _scrollToMessage(String messageId) {
    // البحث عن الرسالة في القائمة
    final messageIdInt = int.tryParse(messageId);
    if (messageIdInt == null) return;

    final index = _chatController.messages.indexWhere((m) => m.id == messageIdInt);
    if (index == -1) return; // الرسالة غير موجودة

    // حساب موضع الرسالة في القائمة
    double offset = 0;
    for (int i = 0; i <= index; i++) {
      // تقدير ارتفاع كل رسالة (يمكن تحسين هذا بقياس الارتفاع الفعلي)
      offset += 80; // ارتفاع تقريبي لكل رسالة
    }

    // التمرير إلى الرسالة مع تأثير بصري
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        offset.clamp(0, _scrollController.position.maxScrollExtent),
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );

      // إظهار تأثير بصري لتمييز الرسالة
      Get.snackbar(
        'تم العثور على الرسالة',
        'تم التمرير إلى الرسالة المطلوبة',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );
    }
  }

  Future<void> _loadMoreMessages() async {
    // ملاحظة: تم تعطيل تحميل المزيد من الرسائل مؤقتاً
    // يمكن إضافة هذه الوظيفة لاحقاً
  }

  Future<void> _sendMessage() async {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) return;

    final content = _textController.text.trim();
    if (content.isEmpty) return;

    try {
      // إرسال الرسالة مع الإشارات
      final success = await _chatController.sendMessage(
        content: content,
        replyToMessageId: _replyToMessage.value?.id,
        mentionedUserIds: _mentionedUserIds.map((id) => id.toString()).toList(),
      );

      if (success) {
        // مسح النص وإعادة تعيين الرد
        _textController.clear();
        _replyToMessage.value = null;
        _mentionedUserIds.clear();

        // تحديث واجهة المستخدم
        setState(() {});

        // التمرير إلى الأسفل بعد الإرسال
        _scrollToBottomDelayed();

        AppLogger.debug('تم إرسال الرسالة بنجاح');
      }
    } catch (e) {
      AppLogger.error('خطأ في إرسال الرسالة', e);
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// تمرير القائمة إلى الأسفل بعد تأخير قصير
  void _scrollToBottomDelayed() {
    // تجنب تكرار التمرير إذا كان قيد التنفيذ بالفعل
    if (_isScrolling) {
      return;
    }

    // تعيين حالة التمرير إلى قيد التنفيذ
    _isScrolling = true;

    // تأخير قصير لضمان اكتمال تحديث القائمة
    Future.delayed(const Duration(milliseconds: 100), () {
      _scrollToBottom();

      // إعادة تعيين حالة التمرير بعد انتهاء التمرير
      Future.delayed(const Duration(milliseconds: 300), () {
        _isScrolling = false;
      });
    });
  }

  /// تمرير القائمة إلى الأسفل بشكل بسيط ومباشر
  void _scrollToBottom() {
    if (!_scrollController.hasClients) {
      AppLogger.warning('محاولة التمرير بدون عملاء للتمرير');

      // محاولة مرة أخرى بعد تأخير قصير
      Future.delayed(const Duration(milliseconds: 200), () {
        if (_scrollController.hasClients) {
          _scrollToBottom();
        }
      });
      return;
    }

    // التأكد من أن القائمة تم بناؤها بالكامل
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        // التمرير إلى الأسفل بشكل مباشر
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
        AppLogger.debug(
            'تم التمرير إلى أقصى موضع: ${_scrollController.position.maxScrollExtent}');
      } catch (e) {
        AppLogger.error('خطأ في التمرير باستخدام animateTo', e);

        // محاولة بديلة باستخدام jumpTo
        try {
          _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
          AppLogger.debug('تم التمرير باستخدام jumpTo');
        } catch (e2) {
          AppLogger.error('خطأ في التمرير باستخدام jumpTo', e2);

          // محاولة أخيرة بعد تأخير إضافي
          Future.delayed(const Duration(milliseconds: 500), () {
            try {
              if (_scrollController.hasClients) {
                _scrollController
                    .jumpTo(_scrollController.position.maxScrollExtent);
              }
            } catch (e3) {
              // تجاهل الخطأ النهائي
            }
          });
        }
      }
    });
  }



  @override
  void dispose() {
    _textController.removeListener(_handleTextChange);
    _textController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _textFieldFocusNode.dispose();

    // إلغاء المؤقت
    _typingTimer?.cancel();
    
    // إرسال إشعار توقف الكتابة عند إغلاق الشاشة
    if (_isTyping) {
      _sendStoppedTypingNotification();
    }

    // مغادرة مجموعة المحادثة
    _leaveChatGroup();

    // إلغاء الاشتراك في إشعارات تحديث الرسائل
    _messagesUpdateSubscription?.dispose();

    super.dispose();
  }

  /// تبديل عرض منتقي الرموز التعبيرية
  void _toggleEmojiPicker() {
    _showEmojiPicker.value = !_showEmojiPicker.value;
    if (_showEmojiPicker.value) {
      // إخفاء لوحة المفاتيح عند عرض منتقي الرموز التعبيرية
      _textFieldFocusNode.unfocus();
    } else {
      // عرض لوحة المفاتيح عند إخفاء منتقي الرموز التعبيرية
      _textFieldFocusNode.requestFocus();
    }
  }

  /// إضافة رمز تعبيري إلى النص
  void _onEmojiSelected(Category? category, Emoji emoji) {
    final text = _textController.text;
    final cursorPosition = _textController.selection.baseOffset;

    // إضافة الرمز التعبيري في موضع المؤشر
    final newText = cursorPosition >= 0
        ? '${text.substring(0, cursorPosition)}${emoji.emoji}${text.substring(cursorPosition)}'
        : text + emoji.emoji;

    // تحديث النص وموضع المؤشر
    _textController.value = TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(
        offset: cursorPosition >= 0
            ? cursorPosition + emoji.emoji.length
            : newText.length,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) {
      return const Scaffold(
        body: Center(child: Text('User not logged in')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: GestureDetector(
          onTap: () {
            Get.to(() => ChatInfoScreen(chatGroup: widget.chatGroup));
          },
          child: Row(
            children: [
              CircleAvatar(
                backgroundColor: widget.chatGroup.isDirectMessage
                    ? AppColors.accent
                    : AppColors.primary,
                radius: 16,
                child: widget.chatGroup.avatarUrl != null
                    ? Image.network(widget.chatGroup.avatarUrl!)
                    : Text(
                        widget.chatGroup.name.substring(0, 1).toUpperCase(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.chatGroup.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      
                      overflow: TextOverflow.ellipsis,
                    ),
                    Row(
                      children: [
                        Text(
                          widget.chatGroup.isDirectMessage ? 'محادثة مباشرة' : 'مجموعة',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color.fromARGB(255, 239, 186, 186),
                          ),
                        ),
                        const SizedBox(width: 28),
                        // مؤشر حالة الاتصال
                        GetBuilder<UnifiedChatController>(
                          builder: (controller) {
                            final signalRService = Get.find<UnifiedSignalRService>();
                            final isConnected = signalRService.isChatHubConnected;
                            return Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  width: 6,
                                  height: 6,
                                  decoration: BoxDecoration(
                                    color: isConnected ? Colors.green : const Color.fromARGB(255, 235, 158, 43),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 5),
                                Text(
                                  isConnected ? 'متصل' : 'غير متصل',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: isConnected ? const Color.fromARGB(255, 143, 233, 147) : Colors.orange[600],
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          if (_permissionService.canSearchInChat())
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () {
                Get.to(
                    () => SearchConversationScreen(chatGroup: widget.chatGroup));
              },
            ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'add_members':
                
                  Get.to(() => AddMembersScreen(chatGroup: widget.chatGroup));
                  break;
                case 'mute_notifications':
                  Get.to(() =>
                      MuteNotificationsScreen(chatGroup: widget.chatGroup));
                  break;
                case 'info':
                  Get.to(() => ChatInfoScreen(chatGroup: widget.chatGroup));
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'add_members',
                child: Row(
                  children: [
                    Icon(Icons.person_add,color: Colors.black12),
                    SizedBox(width: 8),
                    Text('إضافة أعضاء'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'mute_notifications',
                child: Row(
                  children: [
                    Icon(Icons.notifications_off,color: Colors.black12),
                    SizedBox(width: 8),
                    Text('كتم الإشعارات'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'info',
                child: Row(
                  children: [
                    Icon(Icons.info,color: Colors.black12,),
                    SizedBox(width: 8),
                    Text('معلومات المجموعة'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // قائمة الرسائل
          Expanded(
            child: Obx(() {
              if (_chatController.isLoadingMessages &&
                  _chatController.messages.isEmpty) {
                return const Center(child: CircularProgressIndicator());
              }

              return Stack(
                children: [
                  // قائمة الرسائل
                  ListView.builder(
                    controller: _scrollController,
                    // عدم عكس القائمة لأن ذلك يسبب مشاكل في التمرير
                    reverse: false,
                    padding: const EdgeInsets.only(bottom: 16, top: 8),
                    itemCount: _chatController.messages.length,
                    itemBuilder: (context, index) {
                      final message = _chatController.messages[index];
                      final isCurrentUser = message.senderId == currentUser.id;
                      final showAvatar = index == 0 ||
                          _chatController.messages[index - 1].senderId !=
                              message.senderId;

                      // إضافة سجل تشخيصي للرسالة الأخيرة
                      if (index == _chatController.messages.length - 1) {
                        AppLogger.debug('عرض آخر رسالة: ${message.id}');
                      }

                      return MessageItemWidget(
                        message: message,
                        isCurrentUser: isCurrentUser,
                        showAvatar: showAvatar,
                        currentUserId: currentUser.id.toString(),
                        isAdmin: true, // يمكن تغييره حسب صلاحيات المستخدم
                        onReply: (message) {
                          _replyToMessage.value = message;
                        },
                      );
                    },
                  ),

                  // زر التمرير إلى الأسفل
                  Obx(() {
                    if (_isScrolledUp.value) {
                      return Positioned(
                        bottom: 16,
                        right: 16,
                        child: FloatingActionButton(
                          mini: true,
                          backgroundColor: AppColors.primary,
                          onPressed: _scrollToBottom,
                          child: const Icon(
                            Icons.arrow_downward,
                            color: Colors.white,
                          ),
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  }),

                  // قائمة الإشارات
                  Obx(() {
                    if (_showMentionsList.value && _filteredUsers.isNotEmpty) {
                      return Positioned(
                        bottom: 60,
                        left: 0,
                        right: 0,
                        child: Container(
                          height: 200,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(26),
                                blurRadius: 10,
                                offset: const Offset(0, -5),
                              ),
                            ],
                          ),
                          child: ListView.builder(
                            itemCount: _filteredUsers.length,
                            itemBuilder: (context, index) {
                              final user = _filteredUsers[index];
                              return ListTile(
                                leading: CircleAvatar(
                                  backgroundImage: user.profileImage != null
                                      ? NetworkImage(user.profileImage!)
                                      : null,
                                  child: user.profileImage == null
                                      ? Text(user.name.isNotEmpty
                                          ? user.name[0]
                                          : '?')
                                      : null,
                                ),
                                title: Text(user.name),
                                subtitle: Text(user.email??'غير محدد '),
                                onTap: () => _insertMention(user),
                              );
                            },
                          ),
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  }),
                ],
              );
            }),
          ),

          // الرد على رسالة
          Obx(() {
            if (_replyToMessage.value != null) {
              return Container(
                padding: const EdgeInsets.all(8),
                color: Colors.grey[200],
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'الرد على ${_userController.users.firstWhereOrNull((u) => u.id == _replyToMessage.value!.senderId)?.name ?? 'مستخدم'}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            _replyToMessage.value!.content,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () {
                        _replyToMessage.value = null;
                      },
                    ),
                  ],
                ),
              );
            }
            return const SizedBox.shrink();
          }),

          // مؤشر الكتابة
          GetBuilder<UnifiedChatController>(
            builder: (controller) {
              final typingUsers = controller.getTypingUsers(widget.chatGroup.id);
              if (typingUsers.isNotEmpty) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    typingUsers.length == 1
                        ? '${typingUsers.first} يكتب...'
                        : '${typingUsers.length} أشخاص يكتبون...',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),

          // حقل إدخال الرسالة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(26),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.emoji_emotions),
                  onPressed: _toggleEmojiPicker,
                ),
                if (_permissionService.canUploadAttachments())
                  IconButton(
                    icon: const Icon(Icons.attach_file),
                    onPressed: _showAttachmentOptions,
                  ),
                Expanded(
                  child: TextField(
                    controller: _textController,
                    focusNode: _textFieldFocusNode,
                    decoration: InputDecoration(
                      hintText: 'اكتب رسالة...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.grey[200],
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                    onChanged: (_) => _handleTextChange(),
                    maxLines: 5,
                    minLines: 1,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.send),
                  onPressed: _permissionService.canSendMessages() ? _sendMessage : null,
                ),
              ],
            ),
          ),

          // منتقي الرموز التعبيرية
          Obx(() {
            if (_showEmojiPicker.value) {
              return SizedBox(
                height: 250,
                child: EmojiPicker(
                  onEmojiSelected: _onEmojiSelected,
                  config: Config(
                    height: 250,
                    emojiViewConfig: EmojiViewConfig(
                      columns: 7,
                      emojiSizeMax: 32,
                      verticalSpacing: 0,
                      horizontalSpacing: 0,
                      recentsLimit: 28,
                      backgroundColor: const Color(0xFFF2F2F2),
                      noRecents: const Text(
                        'لا توجد رموز تعبيرية حديثة',
                        style: TextStyle(fontSize: 20, color: Colors.black26),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    categoryViewConfig: CategoryViewConfig(
                      initCategory: Category.RECENT,
                      tabIndicatorAnimDuration: kTabScrollDuration,
                      indicatorColor: Colors.blue,
                      iconColor: Colors.grey,
                      iconColorSelected: Colors.blue,
                      categoryIcons: const CategoryIcons(),
                    ),
                    skinToneConfig: const SkinToneConfig(),
                    bottomActionBarConfig: BottomActionBarConfig(
                      backgroundColor: Colors.blue,
                      buttonIconColor: Colors.white,
                    ),
                  ),
                ),
              );
            }
            return const SizedBox.shrink();
          }),
        ],
      ),
    );
  }

  /// عرض خيارات المرفقات
  void _showAttachmentOptions() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AttachmentOptionsSheet(
        onImageSelected: _handleImageAttachment,
        onFileSelected: _handleFileAttachment,
        onCameraSelected: _handleCameraAttachment,
      ),
    );
  }

  /// معالجة مرفق صورة من المعرض
  Future<void> _handleImageAttachment(File file, String fileName) async {
    await _uploadAttachment(file, fileName, 'صورة من المعرض');
  }

  /// معالجة مرفق ملف
  Future<void> _handleFileAttachment(File file, String fileName) async {
    await _uploadAttachment(file, fileName, 'ملف مرفق');
  }

  /// معالجة مرفق صورة من الكاميرا
  Future<void> _handleCameraAttachment(File file, String fileName) async {
    await _uploadAttachment(file, fileName, 'صورة من الكاميرا');
  }

  /// رفع المرفق
  Future<void> _uploadAttachment(File file, String fileName, String description) async {
    try {
      // عرض مؤشر التحميل
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      // الحصول على معرف المستخدم الحالي
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) {
        throw Exception('لم يتم العثور على المستخدم الحالي');
      }

      // رفع المرفق
      final success = await _attachmentsController.uploadAttachmentFile(
        file: file,
        messageId: 0, // سيتم إنشاء رسالة جديدة للمرفق
        uploadedBy: currentUser.id,
        description: description,
      );

      // إغلاق مؤشر التحميل
      Get.back();

      if (success) {
        // عرض رسالة نجاح
        Get.snackbar(
          'نجاح',
          'تم رفع المرفق بنجاح: $fileName',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
        );

        // TODO: إنشاء رسالة جديدة تحتوي على المرفق
        // await _createMessageWithAttachment(attachment);
      } else {
        throw Exception('فشل في رفع المرفق');
      }
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحاً
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ',
        'فشل في رفع المرفق: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }
}
