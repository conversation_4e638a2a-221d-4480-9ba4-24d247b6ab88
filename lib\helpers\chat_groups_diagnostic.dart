import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../controllers/auth_controller.dart';
import '../services/api/api_service.dart';

/// مساعد تشخيص مجموعات المحادثة
class ChatGroupsDiagnostic {
  static final ApiService _apiService = ApiService();

  /// تشخيص شامل لمجموعات المحادثة
  static Future<void> runFullDiagnostic() async {
    debugPrint('🔍 === بدء التشخيص الشامل لمجموعات المحادثة ===');
    
    try {
      // 1. فحص المستخدم الحالي
      await _checkCurrentUser();
      
      // 2. فحص عضوية المجموعات
      await _checkGroupMembership();
      
      // 3. فحص البيانات من API
      await _checkApiData();
      
    } catch (e) {
      debugPrint('❌ خطأ في التشخيص: $e');
    }
    
    debugPrint('✅ === انتهى التشخيص الشامل ===');
  }

  /// فحص المستخدم الحالي
  static Future<void> _checkCurrentUser() async {
    debugPrint('🔍 فحص المستخدم الحالي...');
    
    try {
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;
      
      if (currentUser == null) {
        debugPrint('❌ لا يوجد مستخدم مسجل');
        return;
      }
      
      debugPrint('✅ المستخدم الحالي:');
      debugPrint('   🆔 المعرف: ${currentUser.id}');
      debugPrint('   👤 الاسم: ${currentUser.name}');
      debugPrint('   📧 البريد: ${currentUser.email}');
      debugPrint('   🏢 القسم: ${currentUser.departmentId ?? "غير محدد"}');
      debugPrint('   ✅ نشط: ${currentUser.isActive}');
      
    } catch (e) {
      debugPrint('❌ خطأ في فحص المستخدم الحالي: $e');
    }
  }

  /// فحص عضوية المجموعات
  static Future<void> _checkGroupMembership() async {
    debugPrint('🔍 فحص عضوية المجموعات...');
    
    try {
      final response = await _apiService.get('/api/ChatGroups/debug/membership');
      
      if (response.statusCode == 200) {
        final data = response.data;
        
        debugPrint('✅ بيانات العضوية:');
        debugPrint('   🆔 معرف المستخدم: ${data['currentUserId']}');
        debugPrint('   📊 إجمالي المجموعات: ${data['allGroupsCount']}');
        debugPrint('   👥 عضوية المستخدم: ${data['userMembershipsCount']}');
        
        // عرض جميع المجموعات
        debugPrint('📋 جميع المجموعات:');
        final allGroups = data['allGroups'] as List;
        for (final group in allGroups) {
          debugPrint('   ${group['id']}. ${group['name']} (منشئ: ${group['createdBy']}, خاصة: ${group['isPrivate']})');
        }
        
        // عرض عضوية المستخدم
        debugPrint('👥 عضوية المستخدم:');
        final memberships = data['userMemberships'] as List;
        if (memberships.isEmpty) {
          debugPrint('   ⚠️ المستخدم ليس عضو في أي مجموعة!');
        } else {
          for (final membership in memberships) {
            final roleText = _getRoleText(membership['role']);
            debugPrint('   مجموعة ${membership['groupId']}: $roleText (انضم: ${membership['joinedAt']})');
          }
        }
        
        // عرض إحصائيات المجموعات
        debugPrint('📊 إحصائيات المجموعات:');
        final stats = data['groupStatistics'] as List;
        for (final stat in stats) {
          debugPrint('   ${stat['groupId']}. ${stat['groupName']}:');
          debugPrint('      👥 أعضاء: ${stat['memberCount']}');
          debugPrint('      💬 رسائل: ${stat['messageCount']}');
          debugPrint('      ✅ المستخدم عضو: ${stat['userIsMember']}');
          debugPrint('      👤 منشئ: ${stat['createdBy']}');
          debugPrint('      🔒 خاصة: ${stat['isPrivate']}');
        }
        
      } else {
        debugPrint('❌ فشل في جلب بيانات العضوية: ${response.statusCode}');
      }
      
    } catch (e) {
      debugPrint('❌ خطأ في فحص عضوية المجموعات: $e');
    }
  }

  /// فحص البيانات من API العادي
  static Future<void> _checkApiData() async {
    debugPrint('🔍 فحص البيانات من API العادي...');
    
    try {
      final response = await _apiService.get('/api/ChatGroups');
      
      if (response.statusCode == 200) {
        final groups = response.data as List;
        
        debugPrint('✅ API العادي:');
        debugPrint('   📊 عدد المجموعات المُرجعة: ${groups.length}');
        
        if (groups.isEmpty) {
          debugPrint('   ⚠️ لا توجد مجموعات مُرجعة من API!');
        } else {
          debugPrint('   📋 المجموعات المُرجعة:');
          for (final group in groups.take(5)) {
            debugPrint('      ${group['id']}. ${group['name']}');
            debugPrint('         👥 أعضاء: ${group['memberCount'] ?? 0}');
            debugPrint('         📬 غير مقروءة: ${group['unreadCount'] ?? 0}');
          }
        }
        
      } else {
        debugPrint('❌ فشل في جلب البيانات من API العادي: ${response.statusCode}');
      }
      
    } catch (e) {
      debugPrint('❌ خطأ في فحص البيانات من API العادي: $e');
    }
  }

  /// تحويل رقم الدور إلى نص
  static String _getRoleText(int role) {
    switch (role) {
      case 1:
        return 'عضو';
      case 2:
        return 'مشرف';
      case 3:
        return 'مدير';
      default:
        return 'غير محدد';
    }
  }

  /// اختبار سريع
  static Future<bool> quickTest() async {
    debugPrint('⚡ اختبار سريع لمجموعات المحادثة...');
    
    try {
      // فحص المستخدم
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;
      
      if (currentUser == null) {
        debugPrint('❌ لا يوجد مستخدم مسجل');
        return false;
      }
      
      // فحص API
      final response = await _apiService.get('/api/ChatGroups');
      
      if (response.statusCode != 200) {
        debugPrint('❌ فشل في الوصول لـ API');
        return false;
      }
      
      final groups = response.data as List;
      debugPrint('✅ الاختبار السريع نجح - تم جلب ${groups.length} مجموعة');
      
      return true;
      
    } catch (e) {
      debugPrint('❌ الاختبار السريع فشل: $e');
      return false;
    }
  }

  /// عرض معلومات النظام
  static void showSystemInfo() {
    debugPrint('📋 === معلومات نظام مجموعات المحادثة ===');
    debugPrint('🏗️ الإصدار: 2.0.0');
    debugPrint('📅 تاريخ الإصلاح: 2025-01-14');
    debugPrint('🔧 الحالة: تم إصلاح مشكلة ظهور المجموعات للجميع');
    debugPrint('📝 التحسينات:');
    debugPrint('  - فلترة المجموعات حسب العضوية');
    debugPrint('  - إضافة تشخيص شامل');
    debugPrint('  - إصلاح عضوية المنشئ التلقائية');
    debugPrint('  - تحسين استعلامات قاعدة البيانات');
    debugPrint('📋 === نهاية المعلومات ===');
  }

  /// إصلاح سريع للمشاكل الشائعة
  static Future<void> quickFix() async {
    debugPrint('🔧 بدء الإصلاح السريع...');
    
    try {
      // يمكن إضافة إصلاحات تلقائية هنا
      debugPrint('ℹ️ لا توجد إصلاحات تلقائية متاحة حالي<|im_start|>');
      debugPrint('💡 يرجى تشغيل SQL Script: FixChatGroupMembership.sql');
      
    } catch (e) {
      debugPrint('❌ خطأ في الإصلاح السريع: $e');
    }
  }
}
