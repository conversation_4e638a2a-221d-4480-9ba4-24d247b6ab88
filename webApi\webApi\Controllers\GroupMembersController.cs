using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.SignalR;
using webApi.Models;
using webApi.Models.DTOs;
using webApi.Hubs;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة أعضاء المجموعات
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class GroupMembersController : ControllerBase
    {
        private readonly TasksDbContext _context;
        private readonly ILogger<GroupMembersController> _logger;
        private readonly IHubContext<ChatHub> _chatHubContext;

        public GroupMembersController(
            TasksDbContext context, 
            ILogger<GroupMembersController> logger,
            IHubContext<ChatHub> chatHubContext)
        {
            _context = context;
            _logger = logger;
            _chatHubContext = chatHubContext;
        }

        /// <summary>
        /// الحصول على جميع أعضاء المجموعات
        /// </summary>
        /// <returns>قائمة بجميع أعضاء المجموعات</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<object>>> GetGroupMembers()
        {
            try
            {
                _logger.LogInformation("جلب جميع أعضاء المجموعات");

                var groupMembers = await _context.GroupMembers
                    .Where(gm => !gm.IsDeleted)
                    .Join(_context.ChatGroups.Where(g => !g.IsArchived),
                        gm => gm.GroupId,
                        g => g.Id,
                        (gm, g) => new { GroupMember = gm, Group = g })
                    .Join(_context.Users.Where(u => !u.IsDeleted && u.IsActive),
                        x => x.GroupMember.UserId,
                        u => u.Id,
                        (x, u) => new
                        {
                            x.GroupMember.Id,
                            x.GroupMember.GroupId,
                            GroupName = x.Group.Name,
                            x.GroupMember.UserId,
                            x.GroupMember.Role,
                            x.GroupMember.JoinedAt,
                            x.GroupMember.IsDeleted,
                            x.GroupMember.LeftAt,
                            // ✅ إرسال كائن User كامل
                            User = new
                            {
                                u.Id,
                                u.Name,
                                u.Email,
                                ProfileImage = u.ProfileImage,
                                u.IsActive,
                                u.IsOnline,
                                u.CreatedAt
                            }
                        })
                    .OrderBy(gm => gm.GroupName)
                    .ThenBy(gm => gm.User.Name)
                    .ToListAsync();

                _logger.LogInformation("تم جلب {Count} عضو مجموعة", groupMembers.Count);
                return Ok(groupMembers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب أعضاء المجموعات");
                // إرجاع قائمة فارغة بدلاً من خطأ 500
                return Ok(new List<object>());
            }
        }

        /// <summary>
        /// الحصول على عضو مجموعة محدد
        /// </summary>
        /// <param name="id">معرف عضو المجموعة</param>
        /// <returns>عضو المجموعة</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<GroupMember>> GetGroupMember(int id)
        {
            try
            {
                var groupMember = await _context.GroupMembers
                    .Include(gm => gm.Group)
                    .Include(gm => gm.User)
                    .FirstOrDefaultAsync(gm => gm.Id == id);

                if (groupMember == null)
                {
                    return NotFound("عضو المجموعة غير موجود");
                }

                return Ok(groupMember);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب عضو المجموعة {Id}", id);
                return StatusCode(500, "خطأ داخلي في الخادم");
            }
        }

        /// <summary>
        /// الحصول على أعضاء مجموعة محددة
        /// </summary>
        /// <param name="groupId">معرف المجموعة</param>
        /// <returns>قائمة بأعضاء المجموعة</returns>
        [HttpGet("group/{groupId}")]
        public async Task<ActionResult<IEnumerable<object>>> GetMembersByGroup(int groupId)
        {
            try
            {
                _logger.LogInformation("جلب أعضاء المجموعة {GroupId}", groupId);

                // التحقق من وجود المجموعة
                var groupExists = await _context.ChatGroups.AnyAsync(g => g.Id == groupId && !g.IsArchived);
                if (!groupExists)
                {
                    return NotFound("المجموعة غير موجودة");
                }

                var members = await _context.GroupMembers
                    .Where(gm => gm.GroupId == groupId && !gm.IsDeleted)
                    .Join(_context.Users.Where(u => !u.IsDeleted && u.IsActive),
                        gm => gm.UserId,
                        u => u.Id,
                        (gm, u) => new
                        {
                            gm.Id,
                            gm.GroupId,
                            gm.UserId,
                            gm.Role,
                            gm.JoinedAt,
                            gm.IsDeleted,
                            gm.LeftAt,
                            // ✅ إرسال كائن User كامل
                            User = new
                            {
                                u.Id,
                                u.Name,
                                u.Email,
                                ProfileImage = u.ProfileImage,
                                u.IsActive,
                                u.IsOnline,
                                u.CreatedAt
                            }
                        })
                    .OrderBy(gm => gm.User.Name)
                    .ToListAsync();

                _logger.LogInformation("تم جلب {Count} عضو للمجموعة {GroupId}", members.Count, groupId);

                // تسجيل عينة من البيانات للتشخيص
                if (members.Any())
                {
                    var firstMember = members.First();
                    _logger.LogInformation("عينة من بيانات العضو: UserId={UserId}, UserName={UserName}",
                        firstMember.UserId, firstMember.User.Name);
                }

                return Ok(members);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب أعضاء المجموعة {GroupId}", groupId);
                return StatusCode(500, "خطأ داخلي في الخادم");
            }
        }

        /// <summary>
        /// الحصول على مجموعات مستخدم محدد
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة بمجموعات المستخدم</returns>
        [HttpGet("user/{userId}")]
        public async Task<ActionResult<IEnumerable<object>>> GetGroupsByUser(int userId)
        {
            try
            {
                _logger.LogInformation("جلب مجموعات المستخدم {UserId}", userId);

                // التحقق من وجود المستخدم
                var userExists = await _context.Users.AnyAsync(u => u.Id == userId);
                if (!userExists)
                {
                    return NotFound("المستخدم غير موجود");
                }

                var userGroups = await _context.GroupMembers
                    .Where(gm => gm.UserId == userId)
                    .Join(_context.ChatGroups.Where(g => !g.IsArchived),
                        gm => gm.GroupId,
                        g => g.Id,
                        (gm, g) => new
                        {
                            gm.Id,
                            gm.GroupId,
                            GroupName = g.Name,
                            GroupDescription = g.Description,
                            GroupIsPrivate = g.IsPrivate,
                            GroupIsDirectMessage = g.IsDirectMessage,
                            GroupImageUrl = g.ImageUrl,
                            gm.UserId,
                            gm.Role,
                            gm.JoinedAt,
                            gm.IsDeleted,
                            gm.LeftAt
                        })
                    .OrderByDescending(gm => gm.JoinedAt)
                    .ToListAsync();

                _logger.LogInformation("تم جلب {Count} مجموعة للمستخدم {UserId}", userGroups.Count, userId);
                return Ok(userGroups);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب مجموعات المستخدم {UserId}", userId);
                return StatusCode(500, "خطأ داخلي في الخادم");
            }
        }

        /// <summary>
        /// إضافة عضو إلى مجموعة
        /// </summary>
        /// <param name="groupMember">بيانات عضو المجموعة</param>
        /// <returns>عضو المجموعة المُنشأ</returns>
        [HttpPost]
        public async Task<ActionResult<GroupMember>> AddGroupMember(GroupMember groupMember)
        {
            try
            {
                _logger.LogInformation("إضافة عضو {UserId} إلى المجموعة {GroupId}", 
                    groupMember.UserId, groupMember.GroupId);

                // التحقق من وجود المجموعة
                var group = await _context.ChatGroups.FindAsync(groupMember.GroupId);
                if (group == null || group.IsArchived)
                {
                    return NotFound("المجموعة غير موجودة");
                }

                // التحقق من وجود المستخدم
                var user = await _context.Users.FindAsync(groupMember.UserId);
                if (user == null)
                {
                    return NotFound("المستخدم غير موجود");
                }

                // التحقق من عدم وجود العضو مسبقاً
                var existingMember = await _context.GroupMembers
                    .FirstOrDefaultAsync(gm => gm.GroupId == groupMember.GroupId && gm.UserId == groupMember.UserId);

                if (existingMember != null)
                {
                    return BadRequest("المستخدم عضو في المجموعة بالفعل");
                }

                // تعيين القيم الافتراضية
                groupMember.JoinedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                if (groupMember.Role == 0)
                {
                    groupMember.Role = 1; // 1 = member
                }

                _context.GroupMembers.Add(groupMember);
                await _context.SaveChangesAsync();

                // إرسال إشعار عبر SignalR
                await _chatHubContext.Clients.Group(groupMember.GroupId.ToString())
                    .SendAsync("UserJoinedGroup", new
                    {
                        GroupId = groupMember.GroupId,
                        UserId = groupMember.UserId,
                        UserName = user.Name,
                        Role = groupMember.Role,
                        JoinedAt = groupMember.JoinedAt
                    });

                _logger.LogInformation("تم إضافة العضو {UserId} إلى المجموعة {GroupId}", 
                    groupMember.UserId, groupMember.GroupId);

                return CreatedAtAction(nameof(GetGroupMember), new { id = groupMember.Id }, groupMember);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة عضو إلى المجموعة");
                return StatusCode(500, "خطأ داخلي في الخادم");
            }
        }

        /// <summary>
        /// تحديث دور عضو في المجموعة
        /// </summary>
        /// <param name="id">معرف عضو المجموعة</param>
        /// <param name="request">بيانات التحديث</param>
        /// <returns>نتيجة العملية</returns>
        [HttpPut("{id}/role")]
        public async Task<IActionResult> UpdateMemberRole(int id, [FromBody] UpdateRoleRequest request)
        {
            try
            {
                var groupMember = await _context.GroupMembers.FindAsync(id);
                if (groupMember == null)
                {
                    return NotFound("عضو المجموعة غير موجود");
                }

                groupMember.Role = request.Role;
                await _context.SaveChangesAsync();

                // إرسال إشعار عبر SignalR
                await _chatHubContext.Clients.Group(groupMember.GroupId.ToString())
                    .SendAsync("MemberRoleUpdated", new
                    {
                        GroupId = groupMember.GroupId,
                        UserId = groupMember.UserId,
                        NewRole = request.Role
                    });

                _logger.LogInformation("تم تحديث دور العضو {Id} إلى {Role}", id, request.Role);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث دور العضو {Id}", id);
                return StatusCode(500, "خطأ داخلي في الخادم");
            }
        }



        /// <summary>
        /// إزالة عضو من المجموعة
        /// </summary>
        /// <param name="id">معرف عضو المجموعة</param>
        /// <returns>نتيجة العملية</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> RemoveGroupMember(int id)
        {
            try
            {
                var groupMember = await _context.GroupMembers.FindAsync(id);
                if (groupMember == null)
                {
                    return NotFound("عضو المجموعة غير موجود");
                }

                var groupId = groupMember.GroupId;
                var userId = groupMember.UserId;

                _context.GroupMembers.Remove(groupMember);
                await _context.SaveChangesAsync();

                // إرسال إشعار عبر SignalR
                await _chatHubContext.Clients.Group(groupId.ToString())
                    .SendAsync("UserLeftGroup", new
                    {
                        GroupId = groupId,
                        UserId = userId
                    });

                _logger.LogInformation("تم إزالة العضو {Id} من المجموعة", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إزالة العضو {Id}", id);
                return StatusCode(500, "خطأ داخلي في الخادم");
            }
        }
    }




}
