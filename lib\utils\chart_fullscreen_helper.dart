import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/chart_enums.dart';
import 'package:get/get.dart';


import '../routes/app_routes.dart';

/// مساعد لعرض المخططات بملء الشاشة
///
/// يوفر طرقًا مساعدة لعرض المخططات بملء الشاشة بطريقة موحدة
class ChartFullscreenHelper {
  /// عرض المخطط بملء الشاشة
  ///
  /// يعرض المخطط بملء الشاشة باستخدام مكون FullscreenChartView
  /// 
  /// المعلمات:
  /// * [context] - سياق البناء
  /// * [title] - عنوان المخطط
  /// * [chartKey] - مفتاح المخطط
  /// * [chartContent] - محتوى المخطط
  /// * [chartType] - نوع المخطط الحالي
  /// * [startDate] - تاريخ بداية الفلتر
  /// * [endDate] - تاريخ نهاية الفلتر
  /// * [filterType] - نوع الفلتر
  /// * [onChartTypeChanged] - دالة يتم استدعاؤها عند تغيير نوع المخطط
  /// * [onFilterChanged] - دالة يتم استدعاؤها عند تغيير الفلتر
  /// * [settings] - إعدادات المخطط
  /// * [onSettingsUpdated] - دالة يتم استدعاؤها عند تحديث الإعدادات
  static void showFullscreenChart({
    required BuildContext context,
    required String title,
    required String chartKey,
    required Widget chartContent,
    ChartType? chartType,
    DateTime? startDate,
    DateTime? endDate,
    TimeFilterType? filterType,
    Function(ChartType, String)? onChartTypeChanged,
    Function(DateTime?, DateTime?, TimeFilterType, String)? onFilterChanged,
    Map<String, dynamic>? settings,
    Function(Map<String, dynamic>)? onSettingsUpdated,
  }) {
    // إنشاء إعدادات افتراضية إذا لم يتم توفيرها
    final Map<String, dynamic> chartSettings = settings ?? {};
    
    // إضافة نوع المخطط إلى الإعدادات إذا لم يكن موجودًا
    if (chartType != null && !chartSettings.containsKey('chartType')) {
      chartSettings['chartType'] = chartType.toString().split('.').last;
    }
    
    // استخدام المسار المسمى لفتح المخطط بملء الشاشة
    Get.toNamed(
      AppRoutes.fullscreenChartView,
      arguments: {
        'title': title,
        'chartKey': chartKey,
        'chartContent': chartContent,
        'chartType': chartType,
        'startDate': startDate,
        'endDate': endDate,
        'filterType': filterType,
        'settings': chartSettings,
        'onChartTypeChanged': onChartTypeChanged != null 
            ? (ChartType type) => onChartTypeChanged(type, chartKey)
            : null,
        'onFilterChanged': onFilterChanged != null
            ? (DateTime? start, DateTime? end, TimeFilterType type) => 
                onFilterChanged(start, end, type, chartKey)
            : null,
        'onSettingsUpdated': onSettingsUpdated,
      },
    );
  }
  
  /// عرض مربع حوار المخطط بملء الشاشة
  ///
  /// يعرض المخطط في مربع حوار بملء الشاشة
  static void showFullscreenChartDialog({
    required BuildContext context,
    required String title,
    required String chartKey,
    required Widget chartContent,
    ChartType? chartType,
    DateTime? startDate,
    DateTime? endDate,
    TimeFilterType? filterType,
    Function(ChartType, String)? onChartTypeChanged,
    Function(DateTime?, DateTime?, TimeFilterType, String)? onFilterChanged,
  }) {
    showDialog(
      context: context,
      builder: (context) => Dialog.fullscreen(
        child: Scaffold(
          appBar: AppBar(
            title: Text(title),
            actions: [
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
          body: Column(
            children: [
              // شريط الفلتر والتصدير
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // يمكن إضافة أزرار التصفية والتصدير هنا
                  ],
                ),
              ),
              
              // محتوى المخطط
              Expanded(
                child: chartContent,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
