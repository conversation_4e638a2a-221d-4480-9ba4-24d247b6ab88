-- إصلاح مشكلة عضوية المجموعات
-- التأكد من أن كل مجموعة لها أعضاء وأن المنشئ عضو فيها

USE [TaskManagementDB]
GO

PRINT '🔍 بدء فحص وإصلاح عضوية المجموعات...'

-- 1. فحص المجموعات بدون أعضاء
PRINT '📊 فحص المجموعات بدون أعضاء:'
SELECT 
    cg.id,
    cg.name,
    cg.created_by,
    cg.is_private,
    cg.is_deleted,
    cg.is_archived,
    (SELECT COUNT(*) FROM group_members gm WHERE gm.group_id = cg.id AND gm.is_deleted = 0 AND gm.left_at IS NULL) as member_count
FROM chat_groups cg
WHERE cg.is_deleted = 0 
    AND cg.is_archived = 0
    AND NOT EXISTS (
        SELECT 1 FROM group_members gm 
        WHERE gm.group_id = cg.id 
            AND gm.is_deleted = 0 
            AND gm.left_at IS NULL
    )
ORDER BY cg.created_at DESC

-- 2. فحص المجموعات التي المنشئ ليس عضو فيها
PRINT '📊 فحص المجموعات التي المنشئ ليس عضو فيها:'
SELECT 
    cg.id,
    cg.name,
    cg.created_by,
    u.name as creator_name,
    cg.is_private
FROM chat_groups cg
LEFT JOIN users u ON cg.created_by = u.id
WHERE cg.is_deleted = 0 
    AND cg.is_archived = 0
    AND NOT EXISTS (
        SELECT 1 FROM group_members gm 
        WHERE gm.group_id = cg.id 
            AND gm.user_id = cg.created_by 
            AND gm.is_deleted = 0 
            AND gm.left_at IS NULL
    )
ORDER BY cg.created_at DESC

-- 3. إصلاح المجموعات بإضافة المنشئ كمدير
PRINT '🔧 إصلاح المجموعات بإضافة المنشئ كمدير...'

DECLARE @fixed_groups INT = 0

-- إضافة المنشئ كمدير للمجموعات التي لا يوجد فيها
INSERT INTO group_members (group_id, user_id, role, joined_at, is_deleted, left_at)
SELECT 
    cg.id,
    cg.created_by,
    3, -- role = 3 (admin)
    cg.created_at,
    0, -- is_deleted = false
    NULL -- left_at = null
FROM chat_groups cg
WHERE cg.is_deleted = 0 
    AND cg.is_archived = 0
    AND cg.created_by IS NOT NULL
    AND EXISTS (SELECT 1 FROM users u WHERE u.id = cg.created_by AND u.is_deleted = 0)
    AND NOT EXISTS (
        SELECT 1 FROM group_members gm 
        WHERE gm.group_id = cg.id 
            AND gm.user_id = cg.created_by 
            AND gm.is_deleted = 0 
            AND gm.left_at IS NULL
    )

SET @fixed_groups = @@ROWCOUNT
PRINT '✅ تم إصلاح ' + CAST(@fixed_groups AS NVARCHAR(10)) + ' مجموعة'

-- 4. فحص نهائي - عرض إحصائيات المجموعات
PRINT '📊 إحصائيات نهائية:'
SELECT 
    'إجمالي المجموعات النشطة' as description,
    COUNT(*) as count
FROM chat_groups 
WHERE is_deleted = 0 AND is_archived = 0

UNION ALL

SELECT 
    'المجموعات مع أعضاء' as description,
    COUNT(DISTINCT cg.id) as count
FROM chat_groups cg
INNER JOIN group_members gm ON cg.id = gm.group_id
WHERE cg.is_deleted = 0 
    AND cg.is_archived = 0
    AND gm.is_deleted = 0 
    AND gm.left_at IS NULL

UNION ALL

SELECT 
    'المجموعات بدون أعضاء' as description,
    COUNT(*) as count
FROM chat_groups cg
WHERE cg.is_deleted = 0 
    AND cg.is_archived = 0
    AND NOT EXISTS (
        SELECT 1 FROM group_members gm 
        WHERE gm.group_id = cg.id 
            AND gm.is_deleted = 0 
            AND gm.left_at IS NULL
    )

-- 5. عرض تفاصيل المجموعات مع عدد الأعضاء
PRINT '📋 تفاصيل المجموعات مع عدد الأعضاء:'
SELECT 
    cg.id,
    cg.name,
    cg.created_by,
    u.name as creator_name,
    cg.is_private,
    cg.is_direct_message,
    (SELECT COUNT(*) FROM group_members gm WHERE gm.group_id = cg.id AND gm.is_deleted = 0 AND gm.left_at IS NULL) as member_count,
    (SELECT COUNT(*) FROM messages m WHERE m.group_id = cg.id AND m.is_deleted = 0) as message_count
FROM chat_groups cg
LEFT JOIN users u ON cg.created_by = u.id
WHERE cg.is_deleted = 0 AND cg.is_archived = 0
ORDER BY cg.created_at DESC

-- 6. فحص أعضاء كل مجموعة
PRINT '👥 أعضاء المجموعات:'
SELECT 
    cg.id as group_id,
    cg.name as group_name,
    gm.user_id,
    u.name as user_name,
    gm.role,
    CASE gm.role 
        WHEN 1 THEN 'عضو'
        WHEN 2 THEN 'مشرف'
        WHEN 3 THEN 'مدير'
        ELSE 'غير محدد'
    END as role_name,
    gm.joined_at,
    gm.is_deleted,
    gm.left_at
FROM chat_groups cg
INNER JOIN group_members gm ON cg.id = gm.group_id
INNER JOIN users u ON gm.user_id = u.id
WHERE cg.is_deleted = 0 
    AND cg.is_archived = 0
    AND gm.is_deleted = 0 
    AND gm.left_at IS NULL
ORDER BY cg.id, gm.role DESC, gm.joined_at

PRINT '✅ انتهى فحص وإصلاح عضوية المجموعات'
