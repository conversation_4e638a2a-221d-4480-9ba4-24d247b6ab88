import 'package:flutter/material.dart';

/// فئة مخططات الألوان للرسوم البيانية
///
/// توفر مجموعات ألوان مختلفة للرسوم البيانية
class ChartColorSchemes {
  /// الألوان الافتراضية
  static List<Color> defaultColors = [
    Colors.blue,
    Colors.green,
    Colors.red,
    Colors.orange,
    Colors.purple,
    Colors.teal,
    Colors.pink,
    Colors.amber,
    Colors.indigo,
    Colors.cyan,
  ];

  /// ألوان هادئة
  static List<Color> pastelColors = [
    const Color(0xFFB5EAD7), // أخضر فاتح
    const Color(0xFFC7CEEA), // أزرق فاتح
    const Color(0xFFFFDFD3), // برتقالي فاتح
    const Color(0xFFFFB7B2), // وردي فاتح
    const Color(0xFFE2F0CB), // أخضر ليموني فاتح
    const Color(0xFFD0E8F2), // أزرق سماوي فاتح
    const Color(0xFFFFF9C4), // أصفر فاتح
    const Color(0xFFDEC2CB), // بنفسجي فاتح
    const Color(0xFFE6D7FF), // أرجواني فاتح
    const Color(0xFFFFE0AC), // ذهبي فاتح
  ];

  /// ألوان زاهية
  static List<Color> brightColors = [
    const Color(0xFF00A8E8), // أزرق زاهي
    const Color(0xFF7AE582), // أخضر زاهي
    const Color(0xFFFF5A5F), // أحمر زاهي
    const Color(0xFFFFBE0B), // أصفر زاهي
    const Color(0xFF9D4EDD), // أرجواني زاهي
    const Color(0xFF00AFB9), // فيروزي زاهي
    const Color(0xFFFF70A6), // وردي زاهي
    const Color(0xFFFFC43D), // برتقالي زاهي
    const Color(0xFF3A86FF), // أزرق سماوي زاهي
    const Color(0xFF8338EC), // بنفسجي زاهي
  ];

  /// ألوان داكنة
  static List<Color> darkColors = [
    const Color(0xFF1A535C), // أزرق داكن
    const Color(0xFF2C5F2D), // أخضر داكن
    const Color(0xFF801336), // أحمر داكن
    const Color(0xFF7D4E57), // بني داكن
    const Color(0xFF4A306D), // أرجواني داكن
    const Color(0xFF0B3954), // كحلي داكن
    const Color(0xFF6B0F1A), // عنابي داكن
    const Color(0xFF5E503F), // بني فاتح داكن
    const Color(0xFF2E294E), // بنفسجي داكن
    const Color(0xFF1B2021), // أسود داكن
  ];

  /// ألوان أحادية (تدرجات لونية)
  static List<Color> monochromeBlueColors = [
    const Color(0xFF0466C8),
    const Color(0xFF0353A4),
    const Color(0xFF023E7D),
    const Color(0xFF002855),
    const Color(0xFF001845),
    const Color(0xFF001233),
    const Color(0xFF33415C),
    const Color(0xFF5C677D),
    const Color(0xFF7D8597),
    const Color(0xFF979DAC),
  ];

  /// ألوان أحادية خضراء
  static List<Color> monochromeGreenColors = [
    const Color(0xFF2D6A4F),
    const Color(0xFF40916C),
    const Color(0xFF52B788),
    const Color(0xFF74C69D),
    const Color(0xFF95D5B2),
    const Color(0xFFB7E4C7),
    const Color(0xFF1B4332),
    const Color(0xFF081C15),
    const Color(0xFF38B000),
    const Color(0xFF008000),
  ];

  /// الحصول على مخطط ألوان حسب الاسم
  static List<Color> getColorScheme(String schemeName) {
    switch (schemeName) {
      case 'pastel':
        return pastelColors;
      case 'bright':
        return brightColors;
      case 'dark':
        return darkColors;
      case 'monochrome_blue':
        return monochromeBlueColors;
      case 'monochrome_green':
        return monochromeGreenColors;
      case 'default':
      default:
        return defaultColors;
    }
  }

  /// الحصول على ألوان متدرجة من لون أساسي
  static List<Color> getMonochromeColors(Color baseColor, {int count = 10}) {
    final List<Color> colors = [];
    final HSLColor hslColor = HSLColor.fromColor(baseColor);

    for (int i = 0; i < count; i++) {
      // تعديل درجة السطوع والتشبع للحصول على تدرجات مختلفة
      final double lightness = 0.3 + (0.5 * i / (count - 1));
      final double saturation = 0.5 + (0.3 * i / (count - 1));
      
      colors.add(hslColor.withLightness(lightness).withSaturation(saturation).toColor());
    }

    return colors;
  }
}
