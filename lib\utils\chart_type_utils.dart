import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/chart_enums.dart';

/// مساعد لأنواع المخططات
///
/// يوفر دوال مساعدة للحصول على أيقونات وتسميات أنواع المخططات
/// تم تحسين هذه الفئة لتكون المصدر الوحيد للحقيقة لجميع معلومات أنواع المخططات
class ChartTypeUtils {
  /// الحصول على أيقونة نوع المخطط
  static IconData getChartTypeIcon(ChartType type) {
    switch (type) {
      case ChartType.pie:
        return Icons.pie_chart;
      case ChartType.bar:
        return Icons.bar_chart;
      case ChartType.line:
        return Icons.show_chart;
      case ChartType.gantt:
        return Icons.stacked_bar_chart;
      case ChartType.area:
        return Icons.area_chart;
      case ChartType.scatter:
        return Icons.scatter_plot;
      case ChartType.radar:
        return Icons.radar;
      case ChartType.bubble:
        return Icons.bubble_chart;
      case ChartType.donut:
        return Icons.donut_large;
      case ChartType.gauge:
        return Icons.speed;
      case ChartType.heatmap:
        return Icons.grid_on;
      case ChartType.table:
        return Icons.table_chart;
      case ChartType.treemap:
        return Icons.dashboard;
      case ChartType.funnel:
        return Icons.filter_alt;
      case ChartType.waterfall:
        return Icons.waterfall_chart;
      case ChartType.network:
        return Icons.share;
      case ChartType.boxplot:
        return Icons.analytics;
      case ChartType.candlestick:
        return Icons.candlestick_chart;
      default:
        return Icons.insert_chart;
    }
  }

  /// الحصول على تسمية نوع المخطط
  static String getChartTypeLabel(ChartType type) {
    switch (type) {
      case ChartType.pie:
        return 'مخطط دائري';
      case ChartType.bar:
        return 'مخطط شريطي';
      case ChartType.line:
        return 'مخطط خطي';
      case ChartType.gantt:
        return 'مخطط جانت';
      case ChartType.area:
        return 'مخطط مساحي';
      case ChartType.scatter:
        return 'مخطط نقطي';
      case ChartType.radar:
        return 'مخطط راداري';
      case ChartType.bubble:
        return 'مخطط فقاعي';
      case ChartType.donut:
        return 'مخطط حلقي';
      case ChartType.gauge:
        return 'مخطط عداد';
      case ChartType.heatmap:
        return 'خريطة حرارية';
      case ChartType.table:
        return 'جدول';
      case ChartType.treemap:
        return 'خريطة شجرية';
      case ChartType.funnel:
        return 'مخطط قمعي';
      case ChartType.waterfall:
        return 'مخطط شلال';
      case ChartType.candlestick:
        return 'مخطط شموع';
      case ChartType.boxplot:
        return 'مخطط صندوقي';
      case ChartType.network:
        return 'مخطط العلاقات';
      case ChartType.sankey:
        return 'مخطط سانكي';
      default:
        return 'مخطط';
    }
  }

  /// الحصول على أنواع المخططات المتاحة بناءً على عنوان المخطط
  static List<ChartType> getAvailableChartTypes(String title) {
    if (title.contains('توزيع المهام حسب الحالة') ||
        title.contains('توزيع المهام حسب الأولوية')) {
      // أنواع المخططات المنفذة فقط لبطاقة "توزيع المهام حسب الحالة"
      return [
        ChartType.pie,
        ChartType.bar,
        ChartType.funnel,
        ChartType.treemap
      ];
    } else if (title.contains('المهام حسب الشهر')) {
      // أنواع المخططات المنفذة فقط لبطاقة "المهام حسب الشهر"
      return [ChartType.line, ChartType.bar, ChartType.treemap];
    } else if (title.contains('المهام حسب القسم')) {
      return [ChartType.bar, ChartType.pie];
    } else if (title.contains('تحليل أداء الأقسام')) {
      return [ChartType.radar, ChartType.bar, ChartType.line];
    } else if (title.contains('تحليل الوقت والجهد')) {
      return [ChartType.bar, ChartType.line, ChartType.pie];
    } else if (title.contains('خريطة حرارية')) {
      return [ChartType.heatmap, ChartType.bar, ChartType.pie];
    } else if (title.contains('تدفق سير العمل')) {
      return [ChartType.funnel, ChartType.sankey, ChartType.pie, ChartType.bar];
    } else if (title.contains('توزيع المهام بالخريطة الشجرية')) {
      return [ChartType.treemap, ChartType.pie, ChartType.bar];
    } else if (title.contains('تحليل مالي')) {
      return [ChartType.bar, ChartType.line, ChartType.pie];
    } else if (title.contains('علاقات المهام')) {
      return [ChartType.network, ChartType.treemap];
    } else if (title.contains('إنتاجية المستخدم')) {
      return [ChartType.bar, ChartType.line, ChartType.radar];
    } else if (title.contains('وقت إكمال المهام')) {
      return [ChartType.line, ChartType.bar, ChartType.area];
    } else {
      // قائمة افتراضية لأنواع المخططات المنفذة فقط
      return [
        ChartType.bar,
        ChartType.line,
        ChartType.pie,
        ChartType.radar,
        ChartType.treemap,
        ChartType.funnel,
        ChartType.gantt,
        ChartType.sankey,
      ];
    }
  }

  /// تحويل نوع المخطط من سلسلة نصية
  static ChartType getChartTypeFromString(String typeStr) {
    switch (typeStr.toLowerCase()) {
      case 'pie':
        return ChartType.pie;
      case 'bar':
        return ChartType.bar;
      case 'line':
        return ChartType.line;
      case 'area':
        return ChartType.area;
      case 'scatter':
        return ChartType.scatter;
      case 'radar':
        return ChartType.radar;
      case 'bubble':
        return ChartType.bubble;
      case 'donut':
        return ChartType.donut;
      case 'gauge':
        return ChartType.gauge;
      case 'gantt':
        return ChartType.gantt;
      case 'heatmap':
        return ChartType.heatmap;
      case 'table':
        return ChartType.table;
      case 'treemap':
        return ChartType.treemap;
      case 'funnel':
        return ChartType.funnel;
      case 'waterfall':
        return ChartType.waterfall;
      case 'candlestick':
        return ChartType.candlestick;
      case 'boxplot':
        return ChartType.boxplot;
      case 'network':
        return ChartType.network;
      default:
        return ChartType.bar;
    }
  }

  /// تحويل نوع المخطط إلى سلسلة نصية
  static String getStringFromChartType(ChartType type) {
    switch (type) {
      case ChartType.pie:
        return 'pie';
      case ChartType.bar:
        return 'bar';
      case ChartType.line:
        return 'line';
      case ChartType.area:
        return 'area';
      case ChartType.scatter:
        return 'scatter';
      case ChartType.radar:
        return 'radar';
      case ChartType.bubble:
        return 'bubble';
      case ChartType.donut:
        return 'donut';
      case ChartType.gauge:
        return 'gauge';
      case ChartType.gantt:
        return 'gantt';
      case ChartType.heatmap:
        return 'heatmap';
      case ChartType.table:
        return 'table';
      case ChartType.treemap:
        return 'treemap';
      case ChartType.funnel:
        return 'funnel';
      case ChartType.waterfall:
        return 'waterfall';
      case ChartType.candlestick:
        return 'candlestick';
      case ChartType.boxplot:
        return 'boxplot';
      case ChartType.network:
        return 'network';
      case ChartType.sankey:
        return 'sankey';
      default:
        return 'bar';
    }
  }
}
