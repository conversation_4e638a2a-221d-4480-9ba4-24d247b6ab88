import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_styles.dart';

/// مساعد لإنشاء Placeholders للمخططات المعطلة
/// يستخدم هذا الملف كحل مؤقت لاستبدال مخططات fl_chart المعطلة
class ChartPlaceholderHelper {
  
  /// إنشاء placeholder للمخطط الدائري
  static Widget buildPieChartPlaceholder({
    String title = 'مخطط دائري',
    double size = 120,
    Color? color,
  }) {
    return Container(
      height: size,
      width: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: color ?? AppColors.primary, width: 3),
        color: (color ?? AppColors.primary).withValues(alpha: 0.1),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.pie_chart,
              color: color ?? AppColors.primary,
              size: size * 0.25,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: AppStyles.bodySmall.copyWith(
                color: color ?? AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// إنشاء placeholder للمخطط الخطي
  static Widget buildLineChartPlaceholder({
    String title = 'مخطط خطي',
    double height = 200,
    Color? color,
  }) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey.shade50,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.show_chart,
              color: color ?? AppColors.primary,
              size: 48,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: AppStyles.titleMedium.copyWith(
                color: color ?? AppColors.primary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              'سيتم تفعيل المخطط قريباً',
              style: AppStyles.bodySmall.copyWith(
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// إنشاء placeholder للمخطط الشريطي
  static Widget buildBarChartPlaceholder({
    String title = 'مخطط شريطي',
    double height = 200,
    Color? color,
  }) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey.shade50,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bar_chart,
              color: color ?? AppColors.primary,
              size: 48,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: AppStyles.titleMedium.copyWith(
                color: color ?? AppColors.primary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              'سيتم تفعيل المخطط قريباً',
              style: AppStyles.bodySmall.copyWith(
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// إنشاء placeholder عام للمخططات
  static Widget buildGenericChartPlaceholder({
    String title = 'مخطط',
    String subtitle = 'سيتم تفعيل المخطط قريباً',
    IconData icon = Icons.analytics,
    double height = 200,
    Color? color,
  }) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey.shade50,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color ?? AppColors.primary,
              size: 48,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: AppStyles.titleMedium.copyWith(
                color: color ?? AppColors.primary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: AppStyles.bodySmall.copyWith(
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// إنشاء قائمة بيانات بسيطة كبديل للمخططات
  static Widget buildDataList({
    required List<MapEntry<String, dynamic>> data,
    String title = 'البيانات',
    Color? color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppStyles.titleMedium.copyWith(
                color: color ?? AppColors.primary,
              ),
            ),
            const SizedBox(height: 12),
            ...data.map((entry) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      entry.key,
                      style: AppStyles.bodyMedium,
                    ),
                  ),
                  Text(
                    entry.value.toString(),
                    style: AppStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color ?? AppColors.primary,
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  /// إنشاء مؤشر تقدم بسيط
  static Widget buildProgressIndicator({
    required double progress,
    String title = 'التقدم',
    Color? color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: progress / 100,
              backgroundColor: Colors.grey.shade200,
              valueColor: AlwaysStoppedAnimation<Color>(color ?? AppColors.primary),
              minHeight: 8,
              borderRadius: BorderRadius.circular(4),
            ),
            const SizedBox(height: 8),
            Text(
              '${progress.toStringAsFixed(1)}%',
              style: AppStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: color ?? AppColors.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
