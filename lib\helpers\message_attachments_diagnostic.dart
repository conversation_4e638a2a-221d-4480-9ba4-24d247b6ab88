import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../controllers/message_attachments_controller.dart';
import '../services/api/message_attachments_api_service.dart';
import '../services/unified_permission_service.dart';

/// مساعد تشخيص نظام مرفقات الرسائل
class MessageAttachmentsDiagnostic {
  
  /// تشخيص شامل لنظام المرفقات
  static Future<void> runFullDiagnostic() async {
    debugPrint('🔍 === بدء التشخيص الشامل لنظام مرفقات الرسائل ===');
    
    // 1. فحص المتحكمات
    await _checkControllers();
    
    // 2. فحص الصلاحيات
    await _checkPermissions();
    
    // 3. فحص API Service
    await _checkApiService();
    
    // 4. فحص Backend Connection
    await _checkBackendConnection();
    
    debugPrint('✅ === انتهى التشخيص الشامل ===');
  }

  /// فحص المتحكمات
  static Future<void> _checkControllers() async {
    debugPrint('🔍 فحص المتحكمات...');
    
    try {
      // فحص MessageAttachmentsController
      final controller = Get.find<MessageAttachmentsController>();
      debugPrint('✅ MessageAttachmentsController موجود');
      debugPrint('📊 عدد المرفقات المحملة: ${controller.allAttachments.length}');
      debugPrint('🔄 حالة التحميل: ${controller.isLoading}');
      debugPrint('❌ الأخطاء: ${controller.error.isEmpty ? "لا توجد" : controller.error}');
    } catch (e) {
      debugPrint('❌ MessageAttachmentsController غير موجود: $e');
    }
  }

  /// فحص الصلاحيات
  static Future<void> _checkPermissions() async {
    debugPrint('🔍 فحص الصلاحيات...');
    
    try {
      final permissionService = Get.find<UnifiedPermissionService>();
      
      final canUpload = permissionService.canUploadAttachments();
      final canDownload = permissionService.canDownloadAttachments();
      final canView = permissionService.canViewAttachments();
      final canDelete = permissionService.canDeleteAttachments();
      final canShare = permissionService.canShareAttachments();
      
      debugPrint('📤 صلاحية الرفع: ${canUpload ? "✅" : "❌"}');
      debugPrint('📥 صلاحية التنزيل: ${canDownload ? "✅" : "❌"}');
      debugPrint('👁️ صلاحية العرض: ${canView ? "✅" : "❌"}');
      debugPrint('🗑️ صلاحية الحذف: ${canDelete ? "✅" : "❌"}');
      debugPrint('📤 صلاحية المشاركة: ${canShare ? "✅" : "❌"}');
      
      if (!canUpload) {
        debugPrint('⚠️ تحذير: لا توجد صلاحية لرفع المرفقات');
      }
      
    } catch (e) {
      debugPrint('❌ خطأ في فحص الصلاحيات: $e');
    }
  }

  /// فحص API Service
  static Future<void> _checkApiService() async {
    debugPrint('🔍 فحص API Service...');
    
    try {
      final apiService = MessageAttachmentsApiService();
      debugPrint('✅ تم إنشاء MessageAttachmentsApiService');
      
      // اختبار استدعاء API
      try {
        final attachments = await apiService.getAllAttachments();
        debugPrint('✅ تم استدعاء getAllAttachments بنجاح');
        debugPrint('📊 عدد المرفقات من API: ${attachments.length}');
        
        if (attachments.isNotEmpty) {
          final firstAttachment = attachments.first;
          debugPrint('📄 أول مرفق: ${firstAttachment.fileName}');
          debugPrint('📏 حجم الملف: ${firstAttachment.fileSize} بايت');
          debugPrint('📅 تاريخ الرفع: ${DateTime.fromMillisecondsSinceEpoch(firstAttachment.uploadedAt * 1000)}');
        }
        
      } catch (e) {
        debugPrint('❌ فشل في استدعاء API: $e');
      }
      
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء API Service: $e');
    }
  }

  /// فحص الاتصال بـ Backend
  static Future<void> _checkBackendConnection() async {
    debugPrint('🔍 فحص الاتصال بـ Backend...');
    
    try {
      // محاولة الوصول لـ endpoint المرفقات
      final apiService = MessageAttachmentsApiService();
      
      // اختبار بسيط للاتصال
      await apiService.getAllAttachments();
      debugPrint('✅ الاتصال بـ Backend يعمل');
      
    } catch (e) {
      debugPrint('❌ مشكلة في الاتصال بـ Backend: $e');
      
      // تحليل نوع الخطأ
      final errorString = e.toString().toLowerCase();
      
      if (errorString.contains('connection')) {
        debugPrint('🔌 مشكلة في الاتصال - تحقق من تشغيل الخادم');
      } else if (errorString.contains('timeout')) {
        debugPrint('⏱️ انتهت مهلة الاتصال - الخادم بطيء أو غير متاح');
      } else if (errorString.contains('404')) {
        debugPrint('🔍 endpoint غير موجود - تحقق من مسار API');
      } else if (errorString.contains('401') || errorString.contains('403')) {
        debugPrint('🔐 مشكلة في المصادقة - تحقق من رمز الوصول');
      } else {
        debugPrint('❓ خطأ غير معروف في Backend');
      }
    }
  }

  /// اختبار سريع للنظام
  static Future<bool> quickTest() async {
    debugPrint('⚡ اختبار سريع لنظام المرفقات...');
    
    try {
      // 1. فحص المتحكم
      final controller = Get.find<MessageAttachmentsController>();
      
      // 2. فحص الصلاحيات
      final permissionService = Get.find<UnifiedPermissionService>();
      final canUpload = permissionService.canUploadAttachments();
      
      // 3. فحص API
      final apiService = MessageAttachmentsApiService();
      await apiService.getAllAttachments();
      
      debugPrint('✅ الاختبار السريع نجح - النظام يعمل');
      return true;
      
    } catch (e) {
      debugPrint('❌ الاختبار السريع فشل: $e');
      return false;
    }
  }

  /// عرض معلومات النظام
  static void showSystemInfo() {
    debugPrint('📋 === معلومات نظام مرفقات الرسائل ===');
    debugPrint('🏗️ الإصدار: 1.0.0');
    debugPrint('📅 تاريخ الإصلاح: 2025-01-14');
    debugPrint('🔧 الحالة: تم الإصلاح حديثاً');
    debugPrint('📝 الميزات:');
    debugPrint('  - رفع المرفقات من المعرض');
    debugPrint('  - رفع المرفقات من الكاميرا');
    debugPrint('  - رفع الملفات العامة');
    debugPrint('  - نظام صلاحيات شامل');
    debugPrint('  - واجهة مستخدم محسنة');
    debugPrint('📋 === نهاية المعلومات ===');
  }

  /// تشخيص مشكلة محددة
  static Future<void> diagnoseProblem(String problem) async {
    debugPrint('🔍 تشخيص مشكلة: $problem');
    
    switch (problem.toLowerCase()) {
      case 'upload':
      case 'رفع':
        await _diagnoseUploadProblem();
        break;
      case 'download':
      case 'تنزيل':
        await _diagnoseDownloadProblem();
        break;
      case 'permissions':
      case 'صلاحيات':
        await _checkPermissions();
        break;
      case 'api':
        await _checkApiService();
        break;
      case 'backend':
        await _checkBackendConnection();
        break;
      default:
        debugPrint('❓ مشكلة غير معروفة - تشغيل التشخيص الشامل');
        await runFullDiagnostic();
    }
  }

  /// تشخيص مشاكل الرفع
  static Future<void> _diagnoseUploadProblem() async {
    debugPrint('🔍 تشخيص مشاكل الرفع...');
    
    // فحص الصلاحيات
    final permissionService = Get.find<UnifiedPermissionService>();
    if (!permissionService.canUploadAttachments()) {
      debugPrint('❌ لا توجد صلاحية للرفع');
      return;
    }
    
    // فحص API Service
    try {
      final apiService = MessageAttachmentsApiService();
      debugPrint('✅ API Service متاح');
    } catch (e) {
      debugPrint('❌ مشكلة في API Service: $e');
      return;
    }
    
    // فحص Backend endpoint
    debugPrint('🔍 فحص endpoint الرفع: /api/MessageAttachments/upload');
    // TODO: إضافة فحص محدد للـ endpoint
    
    debugPrint('✅ تشخيص الرفع مكتمل');
  }

  /// تشخيص مشاكل التنزيل
  static Future<void> _diagnoseDownloadProblem() async {
    debugPrint('🔍 تشخيص مشاكل التنزيل...');
    
    // فحص الصلاحيات
    final permissionService = Get.find<UnifiedPermissionService>();
    if (!permissionService.canDownloadAttachments()) {
      debugPrint('❌ لا توجد صلاحية للتنزيل');
      return;
    }
    
    debugPrint('✅ تشخيص التنزيل مكتمل');
  }
}
