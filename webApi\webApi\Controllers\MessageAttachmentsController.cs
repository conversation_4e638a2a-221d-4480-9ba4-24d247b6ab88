using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة مرفقات الرسائل
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class MessageAttachmentsController : ControllerBase
    {
        private readonly TasksDbContext _context;
        private readonly ILogger<MessageAttachmentsController> _logger;
        private readonly IWebHostEnvironment _environment;

        public MessageAttachmentsController(
            TasksDbContext context, 
            ILogger<MessageAttachmentsController> logger,
            IWebHostEnvironment environment)
        {
            _context = context;
            _logger = logger;
            _environment = environment;
        }

        /// <summary>
        /// الحصول على جميع مرفقات الرسائل
        /// </summary>
        /// <returns>قائمة بجميع مرفقات الرسائل</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<MessageAttachment>>> GetMessageAttachments()
        {
            try
            {
                _logger.LogInformation("جلب جميع مرفقات الرسائل");
                
                var attachments = await _context.MessageAttachments
                    .Where(ma => !ma.IsDeleted)
                    .Include(ma => ma.Message)
                    .Include(ma => ma.UploadedByNavigation)
                    .OrderByDescending(ma => ma.UploadedAt)
                    .ToListAsync();

                _logger.LogInformation("تم جلب {Count} مرفق رسالة", attachments.Count);
                return Ok(attachments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب مرفقات الرسائل");
                return StatusCode(500, "خطأ داخلي في الخادم");
            }
        }

        /// <summary>
        /// الحصول على مرفق رسالة محدد
        /// </summary>
        /// <param name="id">معرف مرفق الرسالة</param>
        /// <returns>مرفق الرسالة</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<MessageAttachment>> GetMessageAttachment(int id)
        {
            try
            {
                var attachment = await _context.MessageAttachments
                    .Include(ma => ma.Message)
                    .Include(ma => ma.UploadedByNavigation)
                    .FirstOrDefaultAsync(ma => ma.Id == id && !ma.IsDeleted);

                if (attachment == null)
                {
                    return NotFound("مرفق الرسالة غير موجود");
                }

                return Ok(attachment);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب مرفق الرسالة {Id}", id);
                return StatusCode(500, "خطأ داخلي في الخادم");
            }
        }

        /// <summary>
        /// الحصول على مرفقات رسالة محددة
        /// </summary>
        /// <param name="messageId">معرف الرسالة</param>
        /// <returns>قائمة بمرفقات الرسالة</returns>
        [HttpGet("message/{messageId}")]
        public async Task<ActionResult<IEnumerable<MessageAttachment>>> GetAttachmentsByMessage(int messageId)
        {
            try
            {
                _logger.LogInformation("جلب مرفقات الرسالة {MessageId}", messageId);

                // التحقق من وجود الرسالة
                var messageExists = await _context.Messages.AnyAsync(m => m.Id == messageId && !m.IsDeleted);
                if (!messageExists)
                {
                    return NotFound("الرسالة غير موجودة");
                }

                var attachments = await _context.MessageAttachments
                    .Where(ma => ma.MessageId == messageId && !ma.IsDeleted)
                    .Include(ma => ma.UploadedByNavigation)
                    .OrderBy(ma => ma.UploadedAt)
                    .ToListAsync();

                _logger.LogInformation("تم جلب {Count} مرفق للرسالة {MessageId}", attachments.Count, messageId);
                return Ok(attachments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب مرفقات الرسالة {MessageId}", messageId);
                return StatusCode(500, "خطأ داخلي في الخادم");
            }
        }

        /// <summary>
        /// رفع مرفق جديد للرسالة
        /// </summary>
        /// <param name="file">الملف المرفق</param>
        /// <param name="messageId">معرف الرسالة</param>
        /// <param name="uploadedBy">معرف المستخدم الذي رفع الملف</param>
        /// <returns>مرفق الرسالة المُنشأ</returns>
        [HttpPost("upload")]
        public async Task<ActionResult<MessageAttachment>> UploadAttachment(
            IFormFile file,
            [FromForm] int messageId,
            [FromForm] int uploadedBy)
        {
            try
            {
                _logger.LogInformation("🔄 بدء رفع مرفق - ملف: {FileName}, حجم: {FileSize}, رسالة: {MessageId}, مستخدم: {UserId}",
                    file?.FileName ?? "null", file?.Length ?? 0, messageId, uploadedBy);

                if (file == null || file.Length == 0)
                {
                    _logger.LogWarning("❌ ملف فارغ أو غير موجود");
                    return BadRequest("لم يتم تحديد ملف للرفع");
                }

                _logger.LogInformation("✅ الملف صالح - الاسم: {FileName}, الحجم: {FileSize}, النوع: {ContentType}",
                    file.FileName, file.Length, file.ContentType);

                // التحقق من وجود الرسالة
                _logger.LogInformation("🔍 التحقق من وجود الرسالة {MessageId}", messageId);
                var message = await _context.Messages.FindAsync(messageId);
                if (message == null || message.IsDeleted)
                {
                    _logger.LogWarning("❌ الرسالة {MessageId} غير موجودة أو محذوفة", messageId);
                    return NotFound("الرسالة غير موجودة");
                }
                _logger.LogInformation("✅ الرسالة {MessageId} موجودة", messageId);

                // التحقق من وجود المستخدم
                _logger.LogInformation("🔍 التحقق من وجود المستخدم {UserId}", uploadedBy);
                var userExists = await _context.Users.AnyAsync(u => u.Id == uploadedBy);
                if (!userExists)
                {
                    _logger.LogWarning("❌ المستخدم {UserId} غير موجود", uploadedBy);
                    return NotFound("المستخدم غير موجود");
                }
                _logger.LogInformation("✅ المستخدم {UserId} موجود", uploadedBy);

                // إنشاء مجلد الرفع إذا لم يكن موجوداً
                _logger.LogInformation("🔍 التحقق من مجلد الرفع - WebRootPath: {WebRootPath}", _environment.WebRootPath);

                var uploadsPath = Path.Combine(_environment.WebRootPath ?? "wwwroot", "uploads", "message-attachments");
                _logger.LogInformation("📁 مسار الرفع: {UploadsPath}", uploadsPath);

                if (!Directory.Exists(uploadsPath))
                {
                    _logger.LogInformation("📁 إنشاء مجلد الرفع: {UploadsPath}", uploadsPath);
                    Directory.CreateDirectory(uploadsPath);
                }
                _logger.LogInformation("✅ مجلد الرفع جاهز");

                // إنشاء اسم ملف فريد
                var fileName = $"{Guid.NewGuid()}_{file.FileName}";
                var filePath = Path.Combine(uploadsPath, fileName);
                _logger.LogInformation("📄 مسار الملف: {FilePath}", filePath);

                // حفظ الملف
                _logger.LogInformation("💾 بدء حفظ الملف");
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }
                _logger.LogInformation("✅ تم حفظ الملف بنجاح");

                // إنشاء مرفق الرسالة
                _logger.LogInformation("💾 إنشاء سجل المرفق في قاعدة البيانات");
                var attachment = new MessageAttachment
                {
                    MessageId = messageId,
                    FileName = file.FileName,
                    FilePath = $"/uploads/message-attachments/{fileName}",
                    FileSize = file.Length,
                    FileType = file.ContentType ?? "application/octet-stream",
                    UploadedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    UploadedBy = uploadedBy,
                    IsDeleted = false
                };

                _context.MessageAttachments.Add(attachment);
                _logger.LogInformation("💾 حفظ التغييرات في قاعدة البيانات");
                await _context.SaveChangesAsync();

                _logger.LogInformation("✅ تم رفع مرفق {FileName} للرسالة {MessageId} بنجاح - معرف المرفق: {AttachmentId}",
                    file.FileName, messageId, attachment.Id);

                return CreatedAtAction(nameof(GetMessageAttachment), new { id = attachment.Id }, attachment);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في رفع مرفق الرسالة - الرسالة: {MessageId}, المستخدم: {UserId}, الملف: {FileName}",
                    messageId, uploadedBy, file?.FileName ?? "unknown");
                _logger.LogError("❌ تفاصيل الخطأ: {ErrorMessage}", ex.Message);
                _logger.LogError("❌ Stack Trace: {StackTrace}", ex.StackTrace);
                return StatusCode(500, $"خطأ داخلي في الخادم: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث مرفق رسالة
        /// </summary>
        /// <param name="id">معرف مرفق الرسالة</param>
        /// <param name="attachment">بيانات مرفق الرسالة المحدثة</param>
        /// <returns>نتيجة العملية</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateMessageAttachment(int id, MessageAttachment attachment)
        {
            if (id != attachment.Id)
            {
                return BadRequest("معرف مرفق الرسالة غير متطابق");
            }

            try
            {
                var existingAttachment = await _context.MessageAttachments.FindAsync(id);
                if (existingAttachment == null || existingAttachment.IsDeleted)
                {
                    return NotFound("مرفق الرسالة غير موجود");
                }

                // تحديث الحقول المسموح بتحديثها فقط
                existingAttachment.FileName = attachment.FileName;
                existingAttachment.FileType = attachment.FileType;

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تحديث مرفق الرسالة {Id}", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث مرفق الرسالة {Id}", id);
                return StatusCode(500, "خطأ داخلي في الخادم");
            }
        }

        /// <summary>
        /// حذف مرفق رسالة (حذف منطقي)
        /// </summary>
        /// <param name="id">معرف مرفق الرسالة</param>
        /// <returns>نتيجة العملية</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteMessageAttachment(int id)
        {
            try
            {
                var attachment = await _context.MessageAttachments.FindAsync(id);
                if (attachment == null || attachment.IsDeleted)
                {
                    return NotFound("مرفق الرسالة غير موجود");
                }

                // حذف منطقي
                attachment.IsDeleted = true;
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم حذف مرفق الرسالة {Id}", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف مرفق الرسالة {Id}", id);
                return StatusCode(500, "خطأ داخلي في الخادم");
            }
        }

        /// <summary>
        /// تحميل ملف مرفق
        /// </summary>
        /// <param name="id">معرف مرفق الرسالة</param>
        /// <returns>الملف</returns>
        [HttpGet("{id}/download")]
        public async Task<IActionResult> DownloadAttachment(int id)
        {
            try
            {
                var attachment = await _context.MessageAttachments.FindAsync(id);
                if (attachment == null || attachment.IsDeleted)
                {
                    return NotFound("مرفق الرسالة غير موجود");
                }

                var filePath = Path.Combine(_environment.WebRootPath, attachment.FilePath.TrimStart('/'));
                if (!System.IO.File.Exists(filePath))
                {
                    return NotFound("الملف غير موجود على الخادم");
                }

                var fileBytes = await System.IO.File.ReadAllBytesAsync(filePath);
                return File(fileBytes, attachment.FileType, attachment.FileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل مرفق الرسالة {Id}", id);
                return StatusCode(500, "خطأ داخلي في الخادم");
            }
        }

        /// <summary>
        /// الحصول على إحصائيات مرفقات الرسائل
        /// </summary>
        /// <returns>إحصائيات مرفقات الرسائل</returns>
        [HttpGet("statistics")]
        public async Task<ActionResult<object>> GetAttachmentStatistics()
        {
            try
            {
                var totalAttachments = await _context.MessageAttachments.CountAsync(ma => !ma.IsDeleted);
                var totalSize = await _context.MessageAttachments
                    .Where(ma => !ma.IsDeleted)
                    .SumAsync(ma => ma.FileSize);

                var fileTypeStats = await _context.MessageAttachments
                    .Where(ma => !ma.IsDeleted)
                    .GroupBy(ma => ma.FileType)
                    .Select(g => new
                    {
                        FileType = g.Key,
                        Count = g.Count(),
                        TotalSize = g.Sum(ma => ma.FileSize)
                    })
                    .OrderByDescending(x => x.Count)
                    .ToListAsync();

                var statistics = new
                {
                    TotalAttachments = totalAttachments,
                    TotalSizeBytes = totalSize,
                    TotalSizeMB = Math.Round(totalSize / (1024.0 * 1024.0), 2),
                    FileTypeStatistics = fileTypeStats
                };

                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب إحصائيات مرفقات الرسائل");
                return StatusCode(500, "خطأ داخلي في الخادم");
            }
        }
    }
}
