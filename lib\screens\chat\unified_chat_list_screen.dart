import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/chat_group_models.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/unified_chat_controller.dart';
import '../../models/user_model.dart';
import '../../routes/app_routes.dart';
import '../../services/api/chat_groups_api_service.dart';
import '../../services/unified_signalr_service.dart';
import '../../services/unified_permission_service.dart';

/// شاشة قائمة المحادثات الموحدة
/// تستخدم وحدة التحكم الموحدة للمحادثات
class UnifiedChatListScreen extends StatefulWidget {
  const UnifiedChatListScreen({super.key});

  @override
  State<UnifiedChatListScreen> createState() => _UnifiedChatListScreenState();
}

class _UnifiedChatListScreenState extends State<UnifiedChatListScreen> {
  final _searchController = TextEditingController();
  final _chatController = Get.find<UnifiedChatController>();
  final _authController = Get.find<AuthController>();
  final _userController = Get.find<UserController>();
  final _chatGroupsApiService = ChatGroupsApiService();
  final _permissionService = Get.find<UnifiedPermissionService>();

  // قائمة المحادثات المفلترة للبحث
  final RxList<ChatGroup> _filteredGroups = <ChatGroup>[].obs;
  // حالة البحث
  final RxBool _isSearching = false.obs;

  // ✅ مؤقت لتحديث قائمة المحادثات دوري<|im_start|>
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();
    _loadChatGroups();

    // إضافة مستمع للبحث
    _searchController.addListener(_filterChatGroups);

    // ✅ إضافة استماع لأحداث SignalR
    _setupSignalRListeners();

    // ✅ إعداد تحديث دوري كل 30 ثانية
    _setupPeriodicRefresh();
  }

  /// تحميل مجموعات المحادثة
  Future<void> _loadChatGroups() async {
    try {
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) {
        Get.snackbar(
          'خطأ',
          'يجب تسجيل الدخول أولاً',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      // تحميل المجموعات
      await _chatController.loadChatGroups();

      // جدولة تحديث قائمة المحادثات المفلترة ليتم بعد اكتمال إطار البناء الحالي
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _filteredGroups.value = _chatController.chatGroups;


      });
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل المحادثات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// تصفية مجموعات المحادثة بناءً على نص البحث
  void _filterChatGroups() {
    final searchText = _searchController.text.toLowerCase();

    // جدولة تحديث المتغيرات ليتم بعد اكتمال إطار البناء الحالي
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (searchText.isEmpty) {
        _filteredGroups.value = _chatController.chatGroups;
        _isSearching.value = false;
      } else {
        _isSearching.value = true;
        _filteredGroups.value = _chatController.chatGroups
            .where((group) => group.name.toLowerCase().contains(searchText))
            .toList();
      }
    });
  }

  /// إعداد مستمعي SignalR لتحديث قائمة المحادثات
  void _setupSignalRListeners() {
    // سيتم إضافة مستمعي SignalR لاحقاً عند الحاجة
    debugPrint('🔄 تم إعداد مستمعي SignalR لقائمة المحادثات');
  }

  /// إعداد تحديث دوري لقائمة المحادثات
  void _setupPeriodicRefresh() {
    // تحديث كل 30 ثانية
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      debugPrint('🔄 تحديث دوري لقائمة المحادثات');
      _loadChatGroups();
    });
  }

  @override
  void dispose() {
    _searchController.removeListener(_filterChatGroups);
    _searchController.dispose();
    _refreshTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Text('المحادثات'),
            const SizedBox(width: 8),
            // مؤشر حالة الاتصال
            GetBuilder<UnifiedChatController>(
              builder: (controller) {
                final signalRService = Get.find<UnifiedSignalRService>();
                final isConnected = signalRService.isChatHubConnected;
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 6,
                      height: 6,
                      decoration: BoxDecoration(
                        color: isConnected ? Colors.green : Colors.orange,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      isConnected ? 'متصل' : 'غير متصل',
                      style: TextStyle(
                        fontSize: 10,
                        color: isConnected ? Colors.green[600] : Colors.orange[600],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
        actions: [
          // زر البحث
          if (_permissionService.canSearchInChat())
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () {
                _isSearching.value = true;
                FocusScope.of(context).requestFocus(FocusNode());
              },
            ),
          // زر التحديث
          if (_permissionService.canAccessChat())
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _loadChatGroups,
            ),
        ],
      ),
      floatingActionButton: _permissionService.canCreateChatGroup()
          ? FloatingActionButton(
              onPressed: () {
                _showNewChatOptions();
              },
              child: const Icon(Icons.add_comment),
            )
          : null,
      body: Column(
        children: [
          // شريط البحث
          Obx(() => Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'بحث في المحادثات',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _isSearching.value
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              _isSearching.value = false;
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade100,
                  ),
                ),
              )),

          // قائمة المحادثات
          Expanded(
            child: RefreshIndicator(
              onRefresh: _loadChatGroups,
              child: Obx(() {
                // عرض مؤشر التحميل عند تحميل المجموعات
                if (_chatController.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                // عرض رسالة عندما لا توجد محادثات
                if (_chatController.chatGroups.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.chat_bubble_outline,
                          size: 64,
                          color: Colors.grey.shade300,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد محادثات بعد',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'ابدأ محادثة جديدة بالضغط على زر الإضافة',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                }

                // عرض رسالة عندما لا توجد نتائج للبحث
                if (_isSearching.value && _filteredGroups.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: Colors.grey.shade300,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد نتائج للبحث',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                // عرض قائمة المحادثات
                return ListView.builder(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: _filteredGroups.length,
                  itemBuilder: (context, index) {
                    final group = _filteredGroups[index];
                    return _buildChatGroupItem(group);
                  },
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  /// عرض خيارات المحادثة الجديدة
  void _showNewChatOptions() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const CircleAvatar(
                backgroundColor: AppColors.primary,
                child: Icon(Icons.person, color: Colors.white),
              ),
              title: const Text('محادثة مباشرة'),
              subtitle: const Text('بدء محادثة مع مستخدم واحد'),
              onTap: () {
                Get.back();
                _showNewDirectMessageDialog();
              },
            ),
            const Divider(),
            ListTile(
              leading: const CircleAvatar(
                backgroundColor: AppColors.accent,
                child: Icon(Icons.group, color: Colors.white),
              ),
              title: const Text('مجموعة جديدة'),
              subtitle: const Text('إنشاء مجموعة محادثة مع عدة مستخدمين'),
              onTap: () {
                Get.back();
                Get.toNamed(AppRoutes.createGroupChat);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// عرض مربع حوار لاختيار مستخدم للمحادثة المباشرة
  void _showNewDirectMessageDialog() async {
    // عرض مؤشر التحميل
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false,
    );

    try {
      // تحميل المستخدمين
      await _userController.loadAllUsers();
      Get.back();

      // إنشاء قائمة المستخدمين المفلترة (استبعاد المستخدم الحالي)
      final currentUserId = _authController.currentUser.value?.id;
      final filteredUsers = _userController.users
          .where((user) => user.id != currentUserId && user.isActive)
          .toList();

      if (filteredUsers.isEmpty) {
        Get.snackbar(
          'تنبيه',
          'لا يوجد مستخدمين متاحين للمحادثة',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      // عرض مربع حوار لاختيار مستخدم
      Get.dialog(
        AlertDialog(
          title: const Text('اختر مستخدم للمحادثة'),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: ListView.builder(
              itemCount: filteredUsers.length,
              itemBuilder: (context, index) {
                final user = filteredUsers[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: AppColors.primary,
                    child: Text(
                      user.name.substring(0, 1).toUpperCase(),
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                  title: Text(user.name),
                  subtitle: Text(user.email ?? 'غير محدد'),
                  onTap: () {
                    Get.back();
                    _startDirectChat(user);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إلغاء'),
            ),
          ],
        ),
      );
    } catch (e) {
      Get.back();
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل المستخدمين: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// بدء محادثة مباشرة مع مستخدم
  Future<void> _startDirectChat(User user) async {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) return;

    // عرض مؤشر التحميل
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false,
    );

    try {
      // البحث عن محادثة مباشرة موجودة أو إنشاء واحدة جديدة
      final chatGroup = await _getOrCreateDirectMessageGroup(currentUser.id, user.id);

      // تحديث قائمة المجموعات
      if (!_chatController.chatGroups.any((g) => g.id == chatGroup.id)) {
        await _chatController.loadChatGroups(); // إعادة تحميل المجموعات
      }

      Get.back(); // إغلاق مؤشر التحميل

      // 🔒 فحص الصلاحيات قبل التنقل - إصلاح ثغرة أمنية
      final permissionService = Get.find<UnifiedPermissionService>();
      if (!permissionService.canAccessChat()) {
        Get.snackbar(
          'غير مسموح',
          'ليس لديك صلاحية للوصول إلى المحادثات',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
          duration: const Duration(seconds: 3),
        );
        return;
      }

      // الانتقال إلى شاشة المحادثة
      Get.toNamed(
        AppRoutes.unifiedChatDetail,
        arguments: {'chatGroup': chatGroup},
      );
    } catch (e) {
      Get.back(); // إغلاق مؤشر التحميل
      Get.snackbar(
        'خطأ',
        'فشل إنشاء المحادثة: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// البحث عن محادثة مباشرة موجودة أو إنشاء واحدة جديدة
  Future<ChatGroup> _getOrCreateDirectMessageGroup(int currentUserId, int otherUserId) async {
    try {
      // البحث عن محادثة مباشرة موجودة للمستخدم الحالي
      final directMessages = await _chatGroupsApiService.getUserDirectMessages(currentUserId);

      // البحث عن محادثة مباشرة مع المستخدم المحدد
      final existingChats = directMessages.where((group) {
        return group.groupMembers?.any((member) => member.userId == otherUserId) ?? false;
      }).toList();

      if (existingChats.isNotEmpty) {
        return existingChats.first;
      }

      // الحصول على اسم المستخدم المستهدف لتسمية المحادثة
      final targetUser = _userController.users.firstWhere(
        (user) => user.id == otherUserId,
        orElse: () => throw Exception('المستخدم المستهدف غير موجود'),
      );

      // ✅ إنشاء محادثة مباشرة جديدة بالطريقة الصحيحة
      final createRequest = CreateChatGroupRequest(
        name: 'محادثة مع ${targetUser.name}',
        description: 'محادثة مباشرة',
        isDirectMessage: true,
        isPrivate: true,
        groupType: 'direct',
        maxMembers: 2,
        initialMemberIds: [otherUserId], // ✅ إضافة المستخدم المستهدف
      );

      debugPrint('إنشاء محادثة مباشرة:');
      debugPrint('- المستخدم الحالي: $currentUserId');
      debugPrint('- المستخدم المستهدف: $otherUserId');
      debugPrint('- اسم المستخدم المستهدف: ${targetUser.name}');

      final createdGroup = await _chatGroupsApiService.createGroup(createRequest);
      if (createdGroup == null) {
        throw Exception('فشل في إنشاء المحادثة المباشرة');
      }

      debugPrint('تم إنشاء المحادثة المباشرة بنجاح: ${createdGroup.id}');
      return createdGroup;
    } catch (e) {
      debugPrint('خطأ في إنشاء المحادثة المباشرة: $e');
      throw Exception('خطأ في إنشاء المحادثة المباشرة: $e');
    }
  }

  /// بناء عنصر مجموعة محادثة
  Widget _buildChatGroupItem(ChatGroup group) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor:
            group.isDirectMessage ? AppColors.accent : AppColors.primary,
        child: Text(
          group.name.substring(0, 1).toUpperCase(),
          style: const TextStyle(color: Colors.white),
        ),
      ),
      title: Text(group.name),
      subtitle: Text(
        group.description ?? 'محادثة',
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // عرض عدد الرسائل غير المقروءة
          if (group.unreadCount > 0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                '${group.unreadCount}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          const SizedBox(height: 4),
          // عرض عدد الأعضاء أو حالة الاتصال
          group.isDirectMessage
              ? Icon(
                  Icons.circle,
                  size: 12,
                  color: Colors.green,
                )
              : Text(
                  '${group.memberCount} عضو',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
        ],
      ),
      onTap: () {
        // 🔒 فحص الصلاحيات قبل التنقل - إصلاح ثغرة أمنية
        final permissionService = Get.find<UnifiedPermissionService>();
        if (!permissionService.canAccessChat()) {
          Get.snackbar(
            'غير مسموح',
            'ليس لديك صلاحية للوصول إلى المحادثات',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red.shade100,
            colorText: Colors.red.shade800,
            duration: const Duration(seconds: 3),
          );
          return;
        }

        Get.toNamed(
          AppRoutes.unifiedChatDetail,
          arguments: {'chatGroup': group},
        );
      },
    );
  }
}
